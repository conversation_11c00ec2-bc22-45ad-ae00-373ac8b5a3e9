/**
 * Composant de démonstration pour tester la configuration Tailwind CSS Magneto
 */

import React from 'react';

interface DemoCardProps {
  title: string;
  description: string;
  type?: 'primary' | 'secondary' | 'success' | 'warning' | 'danger';
}

export const DemoCard: React.FC<DemoCardProps> = ({ 
  title, 
  description, 
  type = 'primary' 
}) => {
  const getBadgeClass = () => {
    switch (type) {
      case 'success':
        return 'badge-success';
      case 'warning':
        return 'badge-warning';
      case 'danger':
        return 'badge-danger';
      default:
        return 'badge-primary';
    }
  };

  return (
    <div className="card-default">
      <div className="flex items-center justify-between mb-4">
        <h3 className="heading-3">{title}</h3>
        <span className={`${getBadgeClass()}`}>
          {type.toUpperCase()}
        </span>
      </div>
      
      <p className="body-medium text-gray-600 mb-4">
        {description}
      </p>
      
      <div className="flex gap-3">
        <button className="btn-primary">
          Action Principale
        </button>
        <button className="btn-secondary">
          Action Secondaire
        </button>
      </div>
      
      <div className="mt-4 pt-4 border-t border-gray-200">
        <div className="flex items-center gap-2">
          <span className="status-dot status-active"></span>
          <span className="body-small">Statut actif</span>
        </div>
      </div>
    </div>
  );
};

export default DemoCard;
