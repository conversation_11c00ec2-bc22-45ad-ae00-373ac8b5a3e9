# MEMORY.md - Système Magneto

## Contexte du Projet

Le système Magneto est une plateforme intégrée de gestion d'audits conçue pour transformer la façon dont les organisations gèrent leur conformité et leurs processus qualité. Elle vise à digitaliser et optimiser l'ensemble du cycle de vie des audits, de la planification à la génération de rapports automatisés.

## Instructions de Développement

### Philosophie et Approche

Vous êtes un expert full-stack developer proficient en TypeScript, React, Next.js, et frameworks UI/UX modernes (Tailwind CSS, Shadcn UI, Radix UI). Votre tâche est de produire le code Next.js le plus optimisé et maintenable, suivant les meilleures pratiques et adhérant aux principes de clean code et d'architecture robuste.

### Objectif

Créer une solution Next.js qui soit non seulement fonctionnelle mais qui adhère aussi aux meilleures pratiques en performance, sécurité et maintenabilité.

### Style de Code et Structure

- Écrire du code TypeScript concis et technique avec des exemples précis
- Utiliser des patterns de programmation fonctionnelle et déclarative ; éviter les classes
- Favoriser l'itération et la modularisation plutôt que la duplication de code
- Utiliser des noms de variables descriptifs avec verbes auxiliaires (ex: `isLoading`, `hasError`)
- Structurer les fichiers avec composants exportés, sous-composants, helpers, contenu statique et types
- Utiliser des minuscules avec tirets pour les noms de répertoires (ex: `components/auth-wizard`)

### Optimisation et Meilleures Pratiques

- Minimiser l'utilisation de `'use client'`, `useEffect`, et `setState` ; favoriser React Server Components (RSC) et les fonctionnalités SSR de Next.js
- Implémenter des imports dynamiques pour le code splitting et l'optimisation
- Utiliser un design responsive avec une approche mobile-first
- Optimiser les images : utiliser le format WebP, inclure les données de taille, implémenter le lazy loading

### Gestion d'Erreurs et Validation

- Prioriser la gestion d'erreurs et les cas limites :
  - Utiliser des retours précoces pour les conditions d'erreur
  - Implémenter des clauses de garde pour gérer les préconditions et états invalides tôt
  - Utiliser des types d'erreur personnalisés pour une gestion cohérente

### UI et Style

#### Couleurs du Projet
- #434D68 (sidebar background)
- #E0483F / #E44C43 (navbar horizontal)
- #FDFDFD (fond espace de travail)
- #F2F2F2
- #2E427D (boutons)
- #6A7289 (menu sélectionné)
- #8D97AE (écriture sidebar)

#### Polices du Projet
- Inter
- Poppins
- Roboto

#### Règles de Style Spécifiques
- Les libellés et informations saisies sont de couleur noire
- Les placeholders sont de couleur grise claire
- La bordure des champs est de couleur grise claire
- Les titres sont de couleur noire
- Les boutons : fond #2E427D et écriture blanche
- Le sidebar : fond #434D68 et écriture #8D97AE, fond du menu sélectionné #6A7289
- Le navbar horizontal : fond #E44C43 et écriture blanche
- Le fond de l'espace de travail : couleur #FDFDFD

### Frameworks UI
- Utiliser des frameworks UI modernes (Tailwind CSS, Shadcn UI, Radix UI) pour le style
- Implémenter un design cohérent et des patterns responsives sur toutes les plateformes
- Implémenter le mode sombre et les schémas de couleurs

### Gestion d'État et Récupération de Données

- Utiliser des solutions modernes de gestion d'état (Zustand, TanStack React Query) pour gérer l'état global et la récupération de données
- Implémenter la validation avec Zod pour la validation de schémas

### Sécurité et Performance

- Implémenter une gestion d'erreurs appropriée, validation des entrées utilisateur et pratiques de codage sécurisé
- Suivre les techniques d'optimisation de performance, comme réduire les temps de chargement et améliorer l'efficacité de rendu

### Tests et Documentation

- Écrire des tests unitaires pour les composants en utilisant Jest et React Testing Library
- Fournir des commentaires clairs et concis pour la logique complexe
- Utiliser des commentaires JSDoc pour les fonctions et composants pour améliorer l'intellisense de l'IDE

### Mémorisation de Contexte

- Utiliser la mémorisation de contexte pour éviter les re-rendus inutiles et améliorer les performances
- Implémenter un provider de contexte qui peut être utilisé pour partager des données entre composants
- Créer et mettre à jour le document MEMORY.md avec les tâches complétées et notes additionnelles utiles pour référence future

## Architecture du Système

### Vision et Objectifs Métier

Magneto vise à :
- **Transformation Digitale de l'Audit** : Digitalisation complète, automatisation intelligente, mobilité terrain, collaboration distribuée
- **Amélioration de la Performance Opérationnelle** : Réduction des délais, amélioration qualité, optimisation ressources, ROI mesurable
- **Renforcement de la Conformité** : Traçabilité complète, gestion des risques, reporting automatisé, amélioration continue

### Acteurs et Rôles Métier

1. **Administrateur Système** : Gestion globale plateforme, administration utilisateurs, configuration organisationnelle
2. **Auditeur** : Planification audits, exécution terrain, documentation observations, génération rapports
3. **Manager/Responsable d'Équipe** : Validation plans audit, supervision équipes, pilotage KPI
4. **Utilisateur Standard** : Consultation audits, traitement actions, mise à jour statut
5. **Responsable Qualité** : Gestion référentiels, pilotage performance, amélioration continue
6. **Responsable Conformité** : Supervision globale actions, gestion échéances critiques, reporting réglementaire

### Stack Technologique

#### Frontend
- **Next.js 14** : Framework React avec App Router et Server Components
- **React 18** : Interface utilisateur avec Server Components et Streaming
- **TypeScript** : Typage statique pour fiabilité et maintenabilité
- **Tailwind CSS** : Framework CSS utilitaire pour design système cohérent
- **Shadcn/ui** : Composants UI avec Radix UI et design moderne

#### Backend
- **Node.js** : Runtime JavaScript serveur
- **SQL Server** : Base de données relationnelle performante Microsoft
- **Prisma ORM** : Object-Relational Mapping avec génération de types
- **Better Auth** : Système d'authentification moderne et sécurisé
- **Zod** : Validation de schémas avec inférence TypeScript

#### Gestion d'État et Cache
- **Zustand** : Store global léger et performant
- **TanStack React Query** : Gestion du cache serveur et synchronisation
- **Redis** : Cache distribué et sessions
- **Vercel KV** : Stockage clé-valeur pour métadonnées

### Structure du Projet

```
magneto/
├── app/                     # Next.js App Router
│   ├── (auth)/             # Routes authentification
│   ├── (dashboard)/        # Routes protégées
│   │   ├── audits/
│   │   ├── reports/
│   │   ├── organizations/
│   │   ├── admin/
│   │   └── analytics/
│   └── api/                # API Routes
├── components/              # Composants réutilisables
│   ├── ui/                 # Composants UI de base
│   ├── forms/              # Composants de formulaires
│   ├── layouts/            # Layouts d'application
│   ├── features/           # Composants métier
│   └── providers/          # Context providers
├── lib/                     # Utilitaires et services
│   ├── auth/
│   ├── database/
│   ├── services/
│   ├── stores/
│   ├── hooks/
│   ├── validations/
│   └── utils/
├── prisma/                  # Configuration Prisma
├── public/                  # Assets statiques
└── tests/                   # Tests automatisés
```

## Fonctionnalités Core (MVP)

### F1 - Gestion des Audits
- Planification guidée avec assistant IA
- Attribution automatique des ressources
- Exécution mobile avec mode offline
- Capture multimédia géolocalisée
- Suivi temps réel de l'avancement

### F2 - Génération de Rapports Intelligente
- Templates adaptatifs par secteur
- Compilation automatique des données
- Export simultané PDF/Word/Excel/PowerPoint
- Prévisualisation interactive
- Intégration automatique des preuves

### F3 - Collaboration Temps Réel
- Édition simultanée des rapports
- Chat intégré par audit
- Système de commentaires contextuels
- Notifications intelligentes
- Gestion des conflits automatique

## Performance Requirements

- **Temps de réponse** : < 200ms pour 95% des requêtes
- **Disponibilité** : 99.9% uptime (SLA)
- **Utilisateurs simultanés** : Support 100+ utilisateurs
- **Scalabilité** : Architecture cloud-native auto-scalable

## Sécurité et Conformité

### Sécurité
- **Authentification** : Multi-facteurs (2FA/MFA)
- **Chiffrement** : End-to-end pour données sensibles
- **API Security** : Rate limiting, JWT tokens
- **Audit Logs** : Traçabilité complète des actions

### Conformité
- **RGPD** : Compliance complète avec right to be forgotten
- **ISO 27001** : Standards sécurité information
- **SOC 2 Type II** : Certification des contrôles

## Métriques et KPIs

### Métriques Produit
- **Utilisateurs Actifs Mensuels (MAU)** : Croissance 20% MoM
- **Daily Active Users (DAU)** : Ratio DAU/MAU > 40%
- **Feature Adoption** : Utilisation fonctionnalités principales > 80%
- **Retention** : 6 mois > 85%, 12 mois > 75%

### Métriques Performance
- **Temps création audit** : Réduction 70% vs baseline
- **Temps génération rapport** : < 2 minutes target
- **Taux d'erreur** : < 1% sur processus critiques
- **Satisfaction utilisateur** : NPS > 50

## Roadmap

### Phase 1 - MVP Core (Q3 2025)
- Authentification et gestion utilisateurs
- Gestion cycle vie audits
- Génération rapports basiques
- Interface mobile responsive
- Collaboration temps réel

### Phase 2 - Intelligence et Analytics (Q4 2025)
- Assistant IA conversationnel
- Analytics prédictives
- Dashboards avancés
- Templates intelligents
- Intégrations prioritaires

### Phase 3 - Scale et Enterprise (Q1-Q2 2026)
- Workflows configurables avancés
- Multi-tenancy enterprise
- Conformité réglementaire étendue
- API publique complète
- White-labeling

## Ressources et Références

- [Next.js Documentation](https://nextjs.org/docs)
- [React Server Components](https://react.dev/reference/react/Component#server-components)
- [Tailwind CSS](https://tailwindcss.com/)
- [Shadcn UI](https://shadcn.com/)
- [Zustand](https://github.com/pmndrs/zustand)
- [TanStack React Query](https://tanstack.com/query/v4)

## Méthodologie de Développement

1. **System 2 Thinking** : Approche analytique rigoureuse, décomposer en parties gérables
2. **Tree of Thoughts** : Évaluer solutions multiples et conséquences
3. **Iterative Refinement** : Considérer améliorations, cas limites, optimisations
4. **Code Review** : Traiter le code final comme une pull request

### Processus
1. **Deep Dive Analysis** : Analyse approfondie des exigences techniques
2. **Planning** : Développer plan clair avec structure architecturale
3. **Implementation** : Implémentation étape par étape selon meilleures pratiques
4. **Review and Optimize** : Review du code pour optimisations
5. **Finalization** : Finaliser le code sécurisé et performant

## Tâches Complétées

### Configuration et Setup
- ✅ **Migration PostgreSQL vers SQL Server** (20 juin 2025)
  - Mise à jour de tous les fichiers d'architecture
  - Configuration Prisma adaptée pour SQL Server
  - Chaînes de connexion et variables d'environnement
  - Documentation de migration créée (MIGRATION_SQL_SERVER.md)
- [ ] Setup initial du projet
- [ ] Configuration de l'architecture
- [ ] Implémentation authentification
- [ ] Développement composants UI de base
- [ ] Système de gestion des audits
- [ ] Génération de rapports
- [ ] Interface mobile
- [ ] Collaboration temps réel

## Notes de Développement

Cette documentation servira de référence constante pour maintenir la cohérence et la qualité du développement du système Magneto. Toutes les décisions techniques et les implémentations doivent être alignées avec ces spécifications.

### Migration SQL Server
- **Date** : 20 juin 2025
- **Raison** : Compatibilité infrastructure Microsoft, performance, support enterprise
- **Impact** : Types de données adaptés, relations optimisées, configuration moteur Prisma
- **Validation** : Tests de connexion et migrations Prisma requis

## État Actuel du Projet

### ✅ Phase 1 : Configuration Base (TERMINÉE)
- [x] Initialisation Next.js 14 avec TypeScript
- [x] Configuration Tailwind CSS avec couleurs Magneto
- [x] Installation et configuration Shadcn/ui 
- [x] Setup ESLint + Prettier
- [x] Configuration des polices (Inter, Poppins, Roboto)

### ✅ Phase 2 : Base de Données (TERMINÉE)
- [x] Configuration Prisma avec SQL Server
- [x] Création du schéma complet Magneto (17 tables)
- [x] Conversion JSON → String @db.Text pour SQL Server
- [x] Résolution des cycles de relations/cascade
- [x] Migration initiale appliquée avec succès
- [x] Validation de toutes les tables créées

### ✅ Phase 3 : Architecture Application (TERMINÉE)
- [x] Configuration des stores Zustand (auth-store, ui-store)
- [x] Setup TanStack Query avec configuration optimisée
- [x] Configuration Better Auth pour SQL Server
- [x] Validation Zod (schemas auth et common)
- [x] Provider global avec ThemeProvider + QueryClient
- [x] Layout principal avec polices et couleurs Magneto

### ✅ Phase 4 : Interface Utilisateur (TERMINÉE)
- [x] Composant Sidebar avec navigation Magneto
- [x] Composant Navbar horizontal avec recherche et profil
- [x] MainLayout responsive avec sidebar mobile
- [x] Page d'accueil (tableau de bord) moderne
- [x] Installation composants Shadcn/ui : Avatar, DropdownMenu, Button, Input, Card, Sonner
- [x] Application fonctionnelle sur http://localhost:3000

### 🔄 Phase 5 : Authentification (EN COURS)
- [ ] Configuration complète Better Auth
- [ ] Pages de connexion/inscription
- [ ] Middleware de protection des routes
- [ ] Intégration auth avec stores Zustand

### 📋 Phase 6 : Fonctionnalités Métier (À VENIR)
- [ ] Gestion des audits (CRUD)
- [ ] Système d'observations
- [ ] Actions correctives
- [ ] Génération de rapports
- [ ] Dashboard analytics
- [ ] Notifications temps réel

### 🧪 Phase 7 : Tests et Optimisation (À VENIR)
- [ ] Tests unitaires (Jest + React Testing Library)
- [ ] Tests d'intégration Prisma
- [ ] Optimisation des performances
- [ ] SEO et meta tags

---

*Dernière mise à jour : 20 juin 2025 - Interface utilisateur de base terminée*
