# Système d'Audit Automatique - Magneto

## Vue d'ensemble

Le système d'audit automatique de Magneto permet de tracer automatiquement toutes les opérations de base de données (CREATE, UPDATE, DELETE) ainsi que d'enregistrer des audits personnalisés pour les actions métier importantes.

## Architecture

### Composants principaux

1. **Middleware Prisma** : Intercepte automatiquement toutes les opérations de base de données
2. **Contexte d'audit** : Système pour associer les opérations aux utilisateurs et à leur contexte
3. **Fonction de logging** : Enregistre les activités dans la table `ActivityLog`
4. **Helpers utilitaires** : Fonctions pour faciliter l'utilisation du système

### Modèle de données

Le système utilise le modèle `ActivityLog` qui contient :

- `id` : Identifiant unique
- `userId` : Utilisateur qui a effectué l'action (optionnel)
- `action` : Type d'action (create, update, delete, login, etc.)
- `entityType` : Type d'entité concernée (User, Audit, Observation, etc.)
- `entityId` : Identifiant de l'entité concernée
- `description` : Description de l'action
- `metadata` : Métadonnées contextuelles (JSON)
- `ipAddress` : Adresse IP de l'utilisateur
- `userAgent` : User-Agent du navigateur
- `timestamp` : Horodatage de l'action

## Utilisation

### 1. Audit automatique

Toutes les opérations CREATE, UPDATE et DELETE sont automatiquement auditées :

```typescript
// Cette opération sera automatiquement auditée
const user = await prisma.user.create({
  data: { email: '<EMAIL>', name: 'Test User' }
});
```

### 2. Définition du contexte d'audit

Pour associer les opérations à un utilisateur :

```typescript
import { setAuditContext, clearAuditContext } from '@/lib/database/connection';

// Définir le contexte
setAuditContext({
  userId: 'user-id',
  ipAddress: '***********',
  userAgent: 'Mozilla/5.0...'
});

// Effectuer les opérations (seront auditées avec ce contexte)
await prisma.user.update({ ... });

// Nettoyer le contexte
clearAuditContext();
```

### 3. Utilisation avec executeWithAudit

Méthode recommandée pour les opérations groupées :

```typescript
import { executeWithAudit } from '@/lib/database/connection';

const result = await executeWithAudit(async () => {
  // Toutes les opérations dans ce bloc seront auditées
  const audit = await prisma.audit.create({ ... });
  const observations = await prisma.observation.createMany({ ... });
  return { audit, observations };
}, {
  userId: 'user-id',
  ipAddress: '***********',
  userAgent: 'Web Application'
});
```

### 4. Audit personnalisé

Pour les actions métier spécifiques :

```typescript
import { createAuditLog, AuditActions, AuditEntities } from '@/lib/database/connection';

await createAuditLog(
  AuditActions.APPROVE,
  AuditEntities.AUDIT,
  'audit-id',
  'Audit approuvé par le responsable qualité',
  { 
    previousStatus: 'in_progress',
    newStatus: 'completed',
    comments: 'Conforme aux exigences'
  }
);
```

## Constantes disponibles

### Actions d'audit (`AuditActions`)

- `CREATE` : Création d'entité
- `UPDATE` : Modification d'entité
- `DELETE` : Suppression d'entité
- `LOGIN` : Connexion utilisateur
- `LOGOUT` : Déconnexion utilisateur
- `VIEW` : Consultation d'entité
- `EXPORT` : Export de données
- `IMPORT` : Import de données
- `APPROVE` : Approbation
- `REJECT` : Rejet
- `SUBMIT` : Soumission
- `ASSIGN` : Assignation
- `COMPLETE` : Finalisation

### Types d'entités (`AuditEntities`)

- `USER` : Utilisateur
- `ORGANIZATION` : Organisation
- `AUDIT` : Audit
- `OBSERVATION` : Observation
- `ACTION` : Action corrective
- `REPORT` : Rapport
- `TEMPLATE` : Modèle
- `NOTIFICATION` : Notification

## Fonctionnalités avancées

### 1. Gestion des données sensibles

Le système supprime automatiquement les données sensibles des logs d'audit :

- Mots de passe
- Tokens
- Secrets
- Clés
- Hash

### 2. Prévention de la récursion

Le système évite automatiquement la récursion infinie en n'auditant pas les opérations sur `ActivityLog`.

### 3. Tolérance aux pannes

Si l'enregistrement d'audit échoue, l'opération principale n'est pas impactée.

### 4. Performance

- Utilisation d'une instance Prisma séparée pour les audits
- Déconnexion automatique après chaque audit
- Assainissement intelligent des données

## Récupération des logs

### Logs par utilisateur

```typescript
const logs = await prisma.activityLog.findMany({
  where: { userId: 'user-id' },
  orderBy: { timestamp: 'desc' },
  take: 50
});
```

### Logs par entité

```typescript
const logs = await prisma.activityLog.findMany({
  where: { 
    entityType: 'Audit',
    entityId: 'audit-id'
  },
  include: {
    user: {
      select: { name: true, email: true }
    }
  }
});
```

### Statistiques d'audit

```typescript
const stats = await prisma.activityLog.groupBy({
  by: ['action'],
  _count: { action: true },
  where: {
    timestamp: {
      gte: new Date('2024-01-01')
    }
  }
});
```

## Sécurité et conformité

### Traçabilité complète

- Qui a fait quoi
- Quand
- Depuis où (IP)
- Avec quel outil (User-Agent)
- Quelles données ont été modifiées

### Protection des données

- Assainissement automatique des données sensibles
- Stockage sécurisé des métadonnées
- Conformité RGPD

### Audit de l'audit

Les logs d'audit eux-mêmes ne sont pas modifiables une fois créés, garantissant l'intégrité de la trace.

## Bonnes pratiques

1. **Toujours définir le contexte** pour les opérations utilisateur
2. **Utiliser executeWithAudit** pour les opérations groupées
3. **Ajouter des audits personnalisés** pour les actions métier importantes
4. **Nettoyer le contexte** après utilisation
5. **Utiliser les constantes** prédéfinies pour la cohérence
6. **Inclure des métadonnées** pertinentes pour le contexte

## Exemples d'implémentation

Voir le fichier `audit-examples.ts` pour des exemples complets d'utilisation dans différents scénarios.
