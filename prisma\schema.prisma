// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "sqlserver"
  url      = env("DATABASE_URL")
}

// ========================================
// MODÈLES D'AUTHENTIFICATION ET UTILISATEURS
// ========================================

model User {
  id            String    @id @default(cuid())
  email         String    @unique
  name          String
  emailVerified DateTime?
  image         String?
  
  // Champs personnalisés Magneto
  role              String    @default("utilisateur") // admin, auditeur, manager, utilisateur, responsable_qualite, responsable_conformite
  organizationId    String
  isActive          Boolean   @default(true)
  lastLoginAt       DateTime?
    // Relations
  organization      Organization @relation(fields: [organizationId], references: [id], onDelete: NoAction, onUpdate: NoAction)
  accounts          Account[]
  sessions          Session[]
  auditAssignments  AuditAssignment[]
  createdAudits     Audit[]      @relation("AuditCreatedBy")
  observations      Observation[]
  actions           Action[]     @relation("ActionAssignedTo")
  createdActions    Action[]     @relation("ActionCreatedBy")
  reportCollaborations ReportCollaboration[]
  activityLogs      ActivityLog[]
  
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@map("users")
}

model Account {
  id                String  @id @default(cuid())
  userId            String
  type              String
  provider          String
  providerAccountId String
  refresh_token     String? @db.Text
  access_token      String? @db.Text
  expires_at        Int?
  token_type        String?
  scope             String?
  id_token          String? @db.Text
  session_state     String?

  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([provider, providerAccountId])
  @@map("accounts")
}

model Session {
  id           String   @id @default(cuid())
  sessionToken String   @unique
  userId       String
  expires      DateTime
  user         User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("sessions")
}

model VerificationToken {
  identifier String
  token      String   @unique
  expires    DateTime

  @@unique([identifier, token])
  @@map("verificationtokens")
}

// ========================================
// MODÈLES ORGANISATIONNELS
// ========================================

model Organization {
  id          String   @id @default(cuid())
  name        String
  domain      String?  @unique
  logo        String?
  settings    String?  @db.Text // Configuration personnalisée JSON as string
  isActive    Boolean  @default(true)
  
  // Relations
  users       User[]
  audits      Audit[]
  templates   Template[]
  referentials Referential[]
  
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  @@map("organizations")
}

// ========================================
// MODÈLES AUDIT
// ========================================

model Audit {
  id              String      @id @default(cuid())
  title           String
  description     String?     @db.Text
  type            String      // conformite, qualite, securite, environnement, financier
  status          String      @default("draft") // draft, planned, in_progress, completed, cancelled
  priority        String      @default("medium") // low, medium, high, critical
  
  // Planification
  startDate       DateTime?
  endDate         DateTime?
  estimatedDuration Int?      // en heures
  
  // Organisation
  organizationId  String
  createdById     String
  referentialId   String?
    // Métadonnées
  metadata        String?     @db.Text // Données spécifiques au type d'audit JSON as string
  
  // Relations
  organization    Organization @relation(fields: [organizationId], references: [id], onUpdate: NoAction, onDelete: NoAction)
  createdBy       User         @relation("AuditCreatedBy", fields: [createdById], references: [id], onDelete: NoAction)
  referential     Referential? @relation(fields: [referentialId], references: [id], onDelete: SetNull)
  assignments     AuditAssignment[]
  observations    Observation[]
  reports         Report[]
  
  createdAt       DateTime     @default(now())
  updatedAt       DateTime     @updatedAt

  @@map("audits")
}

model AuditAssignment {
  id        String   @id @default(cuid())
  auditId   String
  userId    String
  role      String   @default("auditeur") // lead_auditeur, auditeur, expert, observateur
    // Relations
  audit     Audit    @relation(fields: [auditId], references: [id], onDelete: Cascade)
  user      User     @relation(fields: [userId], references: [id], onDelete: NoAction, onUpdate: NoAction)
  
  createdAt DateTime @default(now())

  @@unique([auditId, userId])
  @@map("audit_assignments")
}

model Referential {
  id              String   @id @default(cuid())
  name            String
  version         String
  description     String?  @db.Text
  type            String   // iso9001, iso14001, iso45001, sox, custom
  organizationId  String
  isActive        Boolean  @default(true)
  content         String     @db.Text // Structure des points de contrôle JSON as string
  
  // Relations
  organization    Organization @relation(fields: [organizationId], references: [id], onDelete: Cascade)
  audits          Audit[]
  checkpoints     Checkpoint[]
  
  createdAt       DateTime @default(now())
  updatedAt       DateTime @updatedAt

  @@map("referentials")
}

model Checkpoint {
  id              String   @id @default(cuid())
  referentialId   String
  code            String   // Code unique dans le référentiel
  title           String
  description     String?  @db.Text
  category        String?
  level           Int      @default(1) // Niveau hiérarchique
  parentId        String?  // Pour structure arborescente
  isRequired      Boolean  @default(true)
    // Relations
  referential     Referential @relation(fields: [referentialId], references: [id], onDelete: Cascade)
  parent          Checkpoint? @relation("CheckpointHierarchy", fields: [parentId], references: [id], onDelete: NoAction, onUpdate: NoAction)
  children        Checkpoint[] @relation("CheckpointHierarchy")
  observations    Observation[]
  
  createdAt       DateTime @default(now())
  updatedAt       DateTime @updatedAt

  @@unique([referentialId, code])
  @@map("checkpoints")
}

// ========================================
// MODÈLES OBSERVATIONS ET ACTIONS
// ========================================

model Observation {
  id              String   @id @default(cuid())
  auditId         String
  checkpointId    String?
  userId          String   // Auditeur qui a fait l'observation
  
  title           String
  description     String   @db.Text
  type            String   @default("non_conformite") // non_conformite, amelioration, bonne_pratique, remarque
  severity        String   @default("medium") // low, medium, high, critical
  status          String   @default("open") // open, in_progress, resolved, closed
  
  // Localisation
  location        String?
  gpsCoordinates  String?  // Latitude,Longitude
    // Preuves
  evidence        String?  @db.Text // URLs des fichiers, photos, etc. JSON as string
    // Relations
  audit           Audit       @relation(fields: [auditId], references: [id], onDelete: Cascade)
  checkpoint      Checkpoint? @relation(fields: [checkpointId], references: [id], onDelete: NoAction, onUpdate: NoAction)
  user            User        @relation(fields: [userId], references: [id], onDelete: NoAction, onUpdate: NoAction)
  actions         Action[]
  
  createdAt       DateTime @default(now())
  updatedAt       DateTime @updatedAt

  @@map("observations")
}

model Action {
  id              String   @id @default(cuid())
  observationId   String?  // Peut être null pour les actions préventives
  title           String
  description     String   @db.Text
  type            String   @default("corrective") // corrective, preventive, improvement
  status          String   @default("open") // open, in_progress, completed, cancelled, overdue
  priority        String   @default("medium") // low, medium, high, critical
  
  // Assignation
  assignedToId    String
  createdById     String
  
  // Planification
  dueDate         DateTime?
  completedAt     DateTime?
  estimatedEffort Int?     // en heures
  
  // Validation
  verifiedAt      DateTime?
  verifiedById    String?
    // Suivi
  progress        Int      @default(0) // Pourcentage 0-100
  comments        String?  @db.Text // Commentaires de suivi JSON as string
    // Relations
  observation     Observation? @relation(fields: [observationId], references: [id], onDelete: SetNull, onUpdate: NoAction)
  assignedTo      User         @relation("ActionAssignedTo", fields: [assignedToId], references: [id], onDelete: NoAction, onUpdate: NoAction)
  createdBy       User         @relation("ActionCreatedBy", fields: [createdById], references: [id], onDelete: NoAction, onUpdate: NoAction)
  
  createdAt       DateTime @default(now())
  updatedAt       DateTime @updatedAt

  @@map("actions")
}

// ========================================
// MODÈLES RAPPORTS
// ========================================

model Report {
  id              String   @id @default(cuid())
  auditId         String
  title           String
  description     String?  @db.Text
  type            String   @default("audit") // audit, action_plan, summary, custom
  status          String   @default("draft") // draft, review, approved, published
  templateId      String?
    // Contenu
  content         String   @db.Text // Contenu structuré du rapport JSON as string
  executiveSummary String? @db.Text
  
  // Métadonnées
  version         String   @default("1.0")
  language        String   @default("fr")
  
  // Export
  formats         String?  @db.Text // Formats disponibles et leurs URLs JSON as string
  // Relations
  audit           Audit    @relation(fields: [auditId], references: [id], onDelete: NoAction, onUpdate: NoAction)
  template        Template? @relation(fields: [templateId], references: [id], onDelete: SetNull, onUpdate: NoAction)
  collaborations  ReportCollaboration[]
  
  createdAt       DateTime @default(now())
  updatedAt       DateTime @updatedAt

  @@map("reports")
}

model ReportCollaboration {
  id              String   @id @default(cuid())
  reportId        String
  userId          String
  role            String   @default("reviewer") // editor, reviewer, approver
  status          String   @default("pending") // pending, active, completed
  
  // Permissions
  canEdit         Boolean  @default(false)
  canComment      Boolean  @default(true)
  canApprove      Boolean  @default(false)
    // Relations
  report          Report   @relation(fields: [reportId], references: [id], onDelete: Cascade)
  user            User     @relation(fields: [userId], references: [id], onDelete: NoAction, onUpdate: NoAction)
  
  createdAt       DateTime @default(now())
  updatedAt       DateTime @updatedAt

  @@unique([reportId, userId])
  @@map("report_collaborations")
}

model Template {
  id              String   @id @default(cuid())
  name            String
  description     String?  @db.Text
  type            String   // audit_report, action_plan, checklist, custom
  category        String?  // Par secteur d'activité
  organizationId  String
    // Contenu
  structure       String   @db.Text // Structure du template JSON as string
  defaultContent  String?  @db.Text // Contenu par défaut JSON as string
  
  // Configuration
  isPublic        Boolean  @default(false)
  isActive        Boolean  @default(true)
  version         String   @default("1.0")
  
  // Relations
  organization    Organization @relation(fields: [organizationId], references: [id], onDelete: Cascade)
  reports         Report[]
  
  createdAt       DateTime @default(now())
  updatedAt       DateTime @updatedAt

  @@map("templates")
}

// ========================================
// MODÈLES ANALYTIQUES ET LOGS
// ========================================

model ActivityLog {
  id              String   @id @default(cuid())
  userId          String?
  action          String   // create, update, delete, login, logout, etc.
  entityType      String   // audit, observation, action, report, etc.
  entityId        String?
    // Détails
  description     String?  @db.Text
  metadata        String?  @db.Text // Données contextuelles JSON as string
  
  // Traçabilité
  ipAddress       String?
  userAgent       String?  @db.Text
  
  // Relations
  user            User?    @relation(fields: [userId], references: [id])
  
  timestamp       DateTime @default(now())

  @@map("activity_logs")
}

model Notification {
  id              String   @id @default(cuid())
  userId          String
  type            String   // audit_assigned, action_due, report_ready, etc.
  title           String
  message         String   @db.Text
  
  // État
  isRead          Boolean  @default(false)
  isArchived      Boolean  @default(false)
    // Métadonnées
  relatedEntityType String?
  relatedEntityId   String?
  metadata        String?  @db.Text // JSON as string
  
  // Delivery
  channels        String?  @db.Text // email, push, in_app JSON as string
  deliveredAt     DateTime?
  
  createdAt       DateTime @default(now())
  readAt          DateTime?

  @@map("notifications")
}

// ========================================
// MODÈLES CONFIGURATION ET SYSTÈME
// ========================================

model SystemSetting {
  id              String   @id @default(cuid())
  key             String   @unique
  value           String   @db.Text // JSON as string
  description     String?  @db.Text
  category        String   @default("general")
  isPublic        Boolean  @default(false) // Si accessible côté client
  
  updatedAt       DateTime @updatedAt

  @@map("system_settings")
}
