import { betterAuth } from "better-auth";
import { prismaAdapter } from "better-auth/adapters/prisma";
import { prisma } from "@/lib/database/connection";

/**
 * Configuration Better Auth pour Magneto
 */
export const auth = betterAuth({
  database: prismaAdapter(prisma, {
    provider: "sqlserver",
  }),
  emailAndPassword: {
    enabled: true,
    requireEmailVerification: true,
    sendResetPassword: async (user: any, url: string) => {
      // TODO: Implémenter l'envoi d'email de réinitialisation
      console.log(`Reset password URL for ${user.email}: ${url}`);
    },
    sendVerificationEmail: async (user: any, url: string) => {
      // TODO: Implémenter l'envoi d'email de vérification
      console.log(`Verification URL for ${user.email}: ${url}`);
    },
  },

  socialProviders: {
    google: {
      clientId: process.env.GOOGLE_CLIENT_ID!,
      clientSecret: process.env.GOOGLE_CLIENT_SECRET!,
    },
    microsoft: {
      clientId: process.env.MICROSOFT_CLIENT_ID!,
      clientSecret: process.env.MICROSOFT_CLIENT_SECRET!,
    },
  },
  session: {
    expiresIn: 60 * 60 * 24 * 7, // 7 jours
    updateAge: 60 * 60 * 24, // 1 jour
  },

  user: {
    additionalFields: {
      role: {
        type: "string",
        required: true,
        defaultValue: "utilisateur",
      },
      organizationId: {
        type: "string",
        required: true,
      },
      isActive: {
        type: "boolean",
        required: true,
        defaultValue: true,
      },
      lastLoginAt: {
        type: "date",
        required: false,
      },    },
  },
  advanced: {
    crossSubDomainCookies: {
      enabled: false, // À activer si sous-domaines multiples
    },
  },

  // Configuration des erreurs personnalisées
  onError: (error: any, context: any) => {
    console.error("Better Auth Error:", error);
    
    // Log des erreurs pour monitoring
    // TODO: Intégrer avec service de logging/monitoring
  },
});

/**
 * Types TypeScript pour Better Auth
 */
export type Session = typeof auth.$Infer.Session;
// Export du type User depuis la session pour éviter les erreurs de types
export type User = typeof auth.$Infer.Session.user;
