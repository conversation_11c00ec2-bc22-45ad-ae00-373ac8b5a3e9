'use client';

import { MainLayout } from '@/components/layout';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import {
  BarChart3,
  ClipboardList,
  TrendingUp,
  Users,
  AlertTriangle,
  CheckCircle,
  Clock,
  FileText,
} from 'lucide-react';

/**
 * Données de démonstration pour le tableau de bord
 */
const dashboardStats = [
  {
    title: 'Audits en cours',
    value: '12',
    change: '+2 cette semaine',
    icon: ClipboardList,
    trend: 'up',
  },
  {
    title: 'Actions ouvertes',
    value: '34',
    change: '-5 depuis hier',
    icon: AlertTriangle,
    trend: 'down',
  },
  {
    title: 'Taux de conformité',
    value: '87%',
    change: '+3% ce mois',
    icon: CheckCircle,
    trend: 'up',
  },
  {
    title: 'Utilisateurs actifs',
    value: '156',
    change: '+12 ce mois',
    icon: Users,
    trend: 'up',
  },
];

const recentActivities = [
  {
    id: 1,
    title: 'Audit ISO 9001 - Production',
    description: 'Démarrage de l\'audit prévu pour demain',
    time: 'Il y a 2 heures',
    status: 'pending',
  },
  {
    id: 2,
    title: 'Action corrective #AC-2024-001',
    description: 'Validation terminée par le responsable qualité',
    time: 'Il y a 4 heures',
    status: 'completed',
  },
  {
    id: 3,
    title: 'Rapport d\'audit - Service Client',
    description: 'Rapport généré et envoyé aux parties prenantes',
    time: 'Il y a 1 jour',
    status: 'completed',
  },
];

export default function HomePage() {
  return (
    <MainLayout>
      <div className="space-y-6">
        {/* En-tête */}
        <div>
          <h1 className="text-3xl font-bold text-black">Tableau de bord</h1>
          <p className="text-gray-600 mt-2">
            Vue d'ensemble de vos activités d'audit et de conformité
          </p>
        </div>

        {/* Statistiques principales */}
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
          {dashboardStats.map((stat) => (
            <Card key={stat.title} className="border-gray-200">
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium text-black">
                  {stat.title}
                </CardTitle>
                <stat.icon className="h-4 w-4 text-gray-600" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-black">{stat.value}</div>
                <p className={`text-xs ${
                  stat.trend === 'up' ? 'text-green-600' : 'text-red-600'
                }`}>
                  {stat.change}
                </p>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Contenu principal */}
        <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
          {/* Actions rapides */}
          <Card className="border-gray-200">
            <CardHeader>
              <CardTitle className="text-black">Actions rapides</CardTitle>
              <CardDescription>
                Accédez rapidement aux fonctionnalités principales
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-3">
              <Button className="w-full justify-start" variant="outline">
                <ClipboardList className="mr-2 h-4 w-4" />
                Créer un nouvel audit
              </Button>
              <Button className="w-full justify-start" variant="outline">
                <FileText className="mr-2 h-4 w-4" />
                Générer un rapport
              </Button>
              <Button className="w-full justify-start" variant="outline">
                <AlertTriangle className="mr-2 h-4 w-4" />
                Créer une action corrective
              </Button>
              <Button className="w-full justify-start" variant="outline">
                <BarChart3 className="mr-2 h-4 w-4" />
                Voir les analytics
              </Button>
            </CardContent>
          </Card>

          {/* Activités récentes */}
          <Card className="md:col-span-2 border-gray-200">
            <CardHeader>
              <CardTitle className="text-black">Activités récentes</CardTitle>
              <CardDescription>
                Dernières actions et événements dans votre organisation
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {recentActivities.map((activity) => (
                  <div
                    key={activity.id}
                    className="flex items-start space-x-3 p-3 rounded-lg bg-gray-50 hover:bg-gray-100 transition-colors"
                  >
                    <div className="flex-shrink-0">
                      {activity.status === 'completed' ? (
                        <CheckCircle className="h-5 w-5 text-green-600" />
                      ) : (
                        <Clock className="h-5 w-5 text-orange-600" />
                      )}
                    </div>
                    <div className="flex-1 min-w-0">
                      <p className="text-sm font-medium text-black">
                        {activity.title}
                      </p>
                      <p className="text-sm text-gray-600">
                        {activity.description}
                      </p>
                      <p className="text-xs text-gray-500 mt-1">
                        {activity.time}
                      </p>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Graphiques et métriques */}
        <div className="grid gap-6 md:grid-cols-2">
          <Card className="border-gray-200">
            <CardHeader>
              <CardTitle className="text-black">Tendances mensuelles</CardTitle>
              <CardDescription>
                Évolution de vos métriques d'audit
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="flex items-center justify-center h-48 text-gray-500">
                <div className="text-center">
                  <BarChart3 className="h-12 w-12 mx-auto mb-2" />
                  <p>Graphique à venir</p>
                  <p className="text-xs">Intégration Chart.js prévue</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="border-gray-200">
            <CardHeader>
              <CardTitle className="text-black">Performance équipe</CardTitle>
              <CardDescription>
                Suivi des performances par auditeur
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="flex items-center justify-center h-48 text-gray-500">
                <div className="text-center">
                  <TrendingUp className="h-12 w-12 mx-auto mb-2" />
                  <p>Métriques à venir</p>
                  <p className="text-xs">Dashboard analytics en développement</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </MainLayout>
  );
}
