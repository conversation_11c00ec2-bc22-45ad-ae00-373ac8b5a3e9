import type { <PERSON><PERSON><PERSON> } from "next";
import { <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON> } from "next/font/google";
import "./globals.css";
import { Providers } from "./providers";

const inter = Inter({
  subsets: ["latin"],
  variable: "--font-inter",
  display: "swap",
});

const poppins = Poppins({
  subsets: ["latin"],
  weight: ["300", "400", "500", "600", "700"],
  variable: "--font-poppins",
  display: "swap",
});

const roboto = Roboto({
  subsets: ["latin"],
  weight: ["300", "400", "500", "700"],
  variable: "--font-roboto",
  display: "swap",
});

export const metadata: Metadata = {
  title: "Magneto - Plateforme de Gestion d'Audits",
  description: "Plateforme intégrée de gestion d'audits avec IA, collaboration temps réel et génération automatique de rapports",
  keywords: "audit, conformité, qualité, rapports, collaboration, IA",
  authors: [{ name: "<PERSON>gneto Team" }],
  viewport: "width=device-width, initial-scale=1",
  themeColor: "#2E427D",
  robots: "index, follow",
  openGraph: {
    title: "Magneto - Plateforme de Gestion d'Audits",
    description: "Transformez vos processus d'audit avec l'IA et la collaboration temps réel",
    url: process.env.NEXT_PUBLIC_APP_URL,
    siteName: "Magneto",
    locale: "fr_FR",
    type: "website",
  },
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="fr" suppressHydrationWarning>
      <body
        className={`${inter.variable} ${poppins.variable} ${roboto.variable} font-inter bg-workspace-bg text-black antialiased`}
      >
        <Providers>
          {children}
        </Providers>
      </body>
    </html>
  );
}
