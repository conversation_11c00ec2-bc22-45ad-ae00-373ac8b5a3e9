import { PrismaClient, Prisma } from "@prisma/client";

const globalForPrisma = globalThis as unknown as {
  prisma: PrismaClient | undefined;
};

/**
 * Instance Prisma pour SQL Server avec configuration optimisée pour Magneto
 */
export const prisma =
  globalForPrisma.prisma ??
  new PrismaClient({
    log: process.env.NODE_ENV === "development" ? ["query", "error", "warn"] : ["error"],
    datasources: {
      db: {
        url: process.env.DATABASE_URL,
      },
    },
    // Configuration spécifique pour SQL Server
    __internal: {
      engine: {
        // Configuration du moteur Prisma pour SQL Server
        binaryTargets: ["native", "windows"],
      },
    },
  });

if (process.env.NODE_ENV !== "production") globalForPrisma.prisma = prisma;

/**
 * Middleware pour l'audit automatique des modifications
 */
prisma.$use(async (params: any, next: any) => {
  const start = Date.now();
  
  try {
    const result = await next(params);
    const duration = Date.now() - start;
    
    // Log des requêtes lentes (> 1 seconde)
    if (duration > 1000) {
      console.warn(`Slow query detected: ${params.model}.${params.action} took ${duration}ms`);
    }
    
    // Audit automatique pour certaines actions
    if (["create", "update", "delete"].includes(params.action)) {
      // TODO: Implémenter l'audit automatique
      console.log(`Audit: ${params.model}.${params.action} executed in ${duration}ms`);
    }
    
    return result;
  } catch (error) {
    const duration = Date.now() - start;
    console.error(`Database error: ${params.model}.${params.action} failed after ${duration}ms:`, error);
    throw error;
  }
});

/**
 * Fonction de test de connexion à la base de données
 */
export async function testDatabaseConnection(): Promise<boolean> {
  try {
    await prisma.$connect();
    await prisma.$queryRaw`SELECT 1 as test`;
    console.log("✅ Connexion à SQL Server établie avec succès");
    return true;
  } catch (error) {
    console.error("❌ Erreur de connexion à SQL Server:", error);
    return false;
  } finally {
    await prisma.$disconnect();
  }
}

/**
 * Fonction de nettoyage pour fermer la connexion
 */
export async function disconnectDatabase(): Promise<void> {
  try {
    await prisma.$disconnect();
    console.log("🔌 Connexion à la base de données fermée");
  } catch (error) {
    console.error("Erreur lors de la fermeture de la connexion:", error);
  }
}

/**
 * Fonction d'initialisation de la base de données
 */
export async function initializeDatabase(): Promise<void> {
  try {
    console.log("🔄 Initialisation de la base de données...");
    
    // Test de connexion
    const isConnected = await testDatabaseConnection();
    if (!isConnected) {
      throw new Error("Impossible de se connecter à la base de données");
    }
    
    // Vérification de l'existence des tables principales
    const users = await prisma.user.count();
    console.log(`📊 Base de données initialisée - ${users} utilisateurs trouvés`);
    
  } catch (error) {
    console.error("❌ Erreur lors de l'initialisation de la base de données:", error);
    throw error;
  }
}

/**
 * Types utilitaires pour les requêtes
 */
export type DatabaseTransaction = Parameters<typeof prisma.$transaction>[0];

/**
 * Helper pour les transactions avec retry automatique
 */
export async function executeWithRetry<T>(
  operation: () => Promise<T>,
  maxRetries: number = 3,
  delay: number = 1000
): Promise<T> {
  let lastError: Error;
  let currentDelay = delay;
  
  for (let attempt = 1; attempt <= maxRetries; attempt++) {
    try {
      return await operation();
    } catch (error) {
      lastError = error as Error;
      
      if (attempt === maxRetries) break;
      
      // Retry uniquement pour certaines erreurs SQL Server
      const shouldRetry = 
        error instanceof Error && 
        (error.message.includes("timeout") || 
         error.message.includes("connection") ||
         error.message.includes("deadlock"));
      
      if (!shouldRetry) break;
      
      console.warn(`Tentative ${attempt}/${maxRetries} échouée, retry dans ${currentDelay}ms...`);
      await new Promise(resolve => setTimeout(resolve, currentDelay));
      currentDelay *= 2; // Exponential backoff
    }
  }
  
  throw lastError!;
}
