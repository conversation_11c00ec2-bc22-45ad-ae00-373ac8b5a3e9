'use client';

import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { ReactQueryDevtools } from '@tanstack/react-query-devtools';
import { ThemeProvider } from 'next-themes';
import { useState } from 'react';
import { Toaster } from '@/components/ui/sonner';

/**
 * Composant principal des providers pour l'application Magneto
 * Inclut la gestion de l'état global, des requêtes, du thème et des notifications
 */
export function Providers({ children }: { children: React.ReactNode }) {
  // Création du client React Query avec configuration optimisée
  const [queryClient] = useState(
    () =>
      new QueryClient({
        defaultOptions: {
          queries: {
            // Cache pendant 5 minutes
            staleTime: 5 * 60 * 1000,
            // Garde en cache pendant 10 minutes
            gcTime: 10 * 60 * 1000,
            // Retry intelligent
            retry: (failureCount, error: any) => {
              // Ne pas retry pour les erreurs 4xx
              if (error?.status >= 400 && error?.status < 500) {
                return false;
              }
              // Retry jusqu'à 3 fois pour les autres erreurs
              return failureCount < 3;
            },
            // Refetch en arrière-plan quand la fenêtre reprend le focus
            refetchOnWindowFocus: true,
            // Refetch en arrière-plan quand la connexion est rétablie
            refetchOnReconnect: true,
          },
          mutations: {
            // Retry une fois pour les mutations
            retry: 1,
          },
        },
      })
  );

  return (
    <QueryClientProvider client={queryClient}>
      <ThemeProvider
        attribute="class"
        defaultTheme="light"
        enableSystem
        disableTransitionOnChange
      >
        {children}
        <Toaster 
          position="top-right"
          richColors
          closeButton
          expand={false}
          visibleToasts={5}
        />
      </ThemeProvider>      <ReactQueryDevtools 
        initialIsOpen={false} 
      />
    </QueryClientProvider>
  );
}
