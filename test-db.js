/**
 * Script de test de connexion à SQL Server
 */

const { testDatabaseConnection } = require('./lib/database/connection.ts');

async function main() {
  console.log('🔄 Test de connexion à SQL Server...');
  
  try {
    const isConnected = await testDatabaseConnection();
    
    if (isConnected) {
      console.log('✅ Connexion SQL Server réussie !');
      console.log('🎯 Prêt pour les migrations Prisma');
    } else {
      console.log('❌ Échec de la connexion SQL Server');
      console.log('💡 Vérifiez votre configuration DATABASE_URL dans .env');
      console.log('💡 Assurez-vous que SQL Server est démarré');
    }
  } catch (error) {
    console.error('❌ Erreur lors du test de connexion:', error);
    console.log('\n💡 Solutions possibles:');
    console.log('   - Vérifier que SQL Server est installé et démarré');
    console.log('   - Vérifier les credentials dans DATABASE_URL');
    console.log('   - Créer la base de données "magneto_db" si elle n\'existe pas');
    console.log('   - Vérifier que le port 1433 est ouvert');
  }
  
  process.exit(0);
}

main().catch(console.error);
