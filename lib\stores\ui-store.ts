import { create } from "zustand";
import { devtools } from "zustand/middleware";

interface UIState {
  // Sidebar
  sidebarOpen: boolean;
  sidebarCollapsed: boolean;
  
  // Theme
  theme: "light" | "dark" | "system";
  
  // Loading states
  isLoading: boolean;
  loadingMessage?: string;
  
  // Notifications
  notifications: Notification[];
  
  // Modals
  modals: Record<string, boolean>;
  
  // Actions
  setSidebarOpen: (open: boolean) => void;
  setSidebarCollapsed: (collapsed: boolean) => void;
  toggleSidebar: () => void;
  setTheme: (theme: "light" | "dark" | "system") => void;
  setLoading: (isLoading: boolean, message?: string) => void;
  addNotification: (notification: Omit<Notification, "id" | "timestamp">) => void;
  removeNotification: (id: string) => void;
  clearNotifications: () => void;
  openModal: (modalId: string) => void;
  closeModal: (modalId: string) => void;
  toggleModal: (modalId: string) => void;
}

interface Notification {
  id: string;
  type: "success" | "error" | "warning" | "info";
  title: string;
  message?: string;
  timestamp: Date;
  autoClose?: boolean;
  duration?: number;
}

export const useUIStore = create<UIState>()(
  devtools(
    (set, get) => ({
      // Initial state
      sidebarOpen: true,
      sidebarCollapsed: false,
      theme: "light",
      isLoading: false,
      notifications: [],
      modals: {},

      // Sidebar actions
      setSidebarOpen: (open) =>
        set({ sidebarOpen: open }, false, "ui/setSidebarOpen"),

      setSidebarCollapsed: (collapsed) =>
        set({ sidebarCollapsed: collapsed }, false, "ui/setSidebarCollapsed"),

      toggleSidebar: () => {
        const { sidebarOpen } = get();
        set({ sidebarOpen: !sidebarOpen }, false, "ui/toggleSidebar");
      },

      // Theme actions
      setTheme: (theme) =>
        set({ theme }, false, "ui/setTheme"),

      // Loading actions
      setLoading: (isLoading, message) =>
        set(
          { isLoading, loadingMessage: message },
          false,
          "ui/setLoading"
        ),

      // Notification actions
      addNotification: (notification) => {
        const id = Math.random().toString(36).substring(2, 9);
        const newNotification: Notification = {
          ...notification,
          id,
          timestamp: new Date(),
          autoClose: notification.autoClose ?? true,
          duration: notification.duration ?? 5000,
        };

        set(
          (state) => ({
            notifications: [...state.notifications, newNotification],
          }),
          false,
          "ui/addNotification"
        );

        // Auto remove notification
        if (newNotification.autoClose) {
          setTimeout(() => {
            get().removeNotification(id);
          }, newNotification.duration);
        }
      },

      removeNotification: (id) =>
        set(
          (state) => ({
            notifications: state.notifications.filter((n) => n.id !== id),
          }),
          false,
          "ui/removeNotification"
        ),

      clearNotifications: () =>
        set({ notifications: [] }, false, "ui/clearNotifications"),

      // Modal actions
      openModal: (modalId) =>
        set(
          (state) => ({
            modals: { ...state.modals, [modalId]: true },
          }),
          false,
          "ui/openModal"
        ),

      closeModal: (modalId) =>
        set(
          (state) => ({
            modals: { ...state.modals, [modalId]: false },
          }),
          false,
          "ui/closeModal"
        ),

      toggleModal: (modalId) => {
        const { modals } = get();
        const isOpen = modals[modalId] || false;
        set(
          (state) => ({
            modals: { ...state.modals, [modalId]: !isOpen },
          }),
          false,
          "ui/toggleModal"
        );
      },
    }),
    { name: "ui-store" }
  )
);
