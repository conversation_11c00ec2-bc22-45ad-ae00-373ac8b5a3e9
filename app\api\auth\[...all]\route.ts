import { auth } from "@/lib/auth/config";
import { toNextJsHandler } from "better-auth/next-js";

/**
 * API Route Handler pour Better Auth
 *
 * Cette route gère tous les endpoints d'authentification :
 * - POST /api/auth/sign-in
 * - POST /api/auth/sign-up
 * - POST /api/auth/sign-out
 * - GET /api/auth/session
 * - POST /api/auth/reset-password
 * - POST /api/auth/verify-email
 * - Et tous les autres endpoints Better Auth
 */
const handler = toNextJsHandler(auth);

export { handler as GET, handler as POST };