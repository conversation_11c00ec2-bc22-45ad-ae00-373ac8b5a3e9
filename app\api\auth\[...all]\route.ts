import { auth } from "@/lib/auth/config";
import { toNextJsHandler } from "better-auth/next-js";

/**
 * Route API pour Better Auth
 * 
 * Cette route gère toutes les requêtes d'authentification :
 * - POST /api/auth/sign-in
 * - POST /api/auth/sign-up
 * - POST /api/auth/sign-out
 * - GET /api/auth/session
 * - Et toutes les autres routes Better Auth
 */
export const { GET, POST } = toNextJsHandler(auth);
