import { createAuthClient } from "better-auth/react";

/**
 * Client d'authentification côté client pour Magneto
 * 
 * Ce client est utilisé dans les composants React pour :
 * - Gérer les connexions/déconnexions
 * - Accéder aux informations de session
 * - Gérer l'état d'authentification
 */
export const authClient = createAuthClient({
  baseURL: process.env.NEXT_PUBLIC_BASE_URL || "http://localhost:3000",
});

/**
 * Hook personnalisé pour accéder à la session
 */
export const useSession = () => {
  return authClient.useSession();
};

/**
 * Hook personnalisé pour l'état de chargement de l'authentification
 */
export const useAuthState = () => {
  const { data: session, isPending, error } = useSession();
  
  return {
    user: session?.user || null,
    isLoading: isPending,
    isAuthenticated: !!session?.user,
    error,
  };
};

/**
 * Fonction utilitaire pour vérifier les permissions
 */
export const checkPermission = (
  userRole: string | undefined,
  requiredRoles: string[]
): boolean => {
  if (!userRole) return false;
  return requiredRoles.includes(userRole);
};

/**
 * Fonction utilitaire pour vérifier si l'utilisateur est admin
 */
export const isAdmin = (userRole: string | undefined): boolean => {
  return checkPermission(userRole, ['admin', 'super_admin']);
};

/**
 * Fonction utilitaire pour vérifier si l'utilisateur est auditeur
 */
export const isAuditor = (userRole: string | undefined): boolean => {
  return checkPermission(userRole, ['auditeur', 'admin', 'super_admin']);
};

/**
 * Fonction utilitaire pour vérifier si l'utilisateur est manager
 */
export const isManager = (userRole: string | undefined): boolean => {
  return checkPermission(userRole, ['manager', 'admin', 'super_admin']);
};

/**
 * Types pour les permissions
 */
export const UserRoles = {
  SUPER_ADMIN: 'super_admin',
  ADMIN: 'admin',
  MANAGER: 'manager',
  AUDITEUR: 'auditeur',
  RESPONSABLE_QUALITE: 'responsable_qualite',
  RESPONSABLE_CONFORMITE: 'responsable_conformite',
  UTILISATEUR: 'utilisateur',
} as const;

export type UserRole = typeof UserRoles[keyof typeof UserRoles];
