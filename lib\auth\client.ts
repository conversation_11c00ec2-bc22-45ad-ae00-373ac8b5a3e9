import { createAuthClient } from "better-auth/react";

/**
 * Client d'authentification côté client pour Magneto
 *
 * Ce client est utilisé dans les composants React pour :
 * - Gérer les connexions/déconnexions
 * - Accéder aux informations de session
 * - Gérer l'état d'authentification
 */
export const authClient = createAuthClient({
  baseURL: process.env.NEXT_PUBLIC_BASE_URL || "http://localhost:3000",
});

/**
 * Hook personnalisé pour accéder à la session
 */
export const useSession = () => {
  return authClient.useSession();
};

/**
 * Hook personnalisé pour les actions d'authentification
 */
export const useAuth = () => {
  const session = useSession();

  const signIn = async (email: string, password: string) => {
    try {
      const result = await authClient.signIn.email({
        email,
        password,
      });
      return result;
    } catch (error) {
      console.error("Erreur de connexion:", error);
      throw error;
    }
  };

  const signUp = async (email: string, password: string, name: string, organizationId: string) => {
    try {
      const result = await authClient.signUp.email({
        email,
        password,
        name,
        // Les champs personnalisés seront ajoutés via Better Auth
      });
      return result;
    } catch (error) {
      console.error("Erreur d'inscription:", error);
      throw error;
    }
  };

  const signOut = async () => {
    try {
      await authClient.signOut();
    } catch (error) {
      console.error("Erreur de déconnexion:", error);
      throw error;
    }
  };

  const resetPassword = async (email: string) => {
    try {
      const result = await authClient.forgetPassword({
        email,
        redirectTo: "/auth/reset-password",
      });
      return result;
    } catch (error) {
      console.error("Erreur de réinitialisation:", error);
      throw error;
    }
  };

  return {
    session: session.data,
    isLoading: session.isPending,
    isAuthenticated: !!session.data,
    user: session.data?.user,
    signIn,
    signUp,
    signOut,
    resetPassword,
  };
};

/**
 * Types pour l'authentification Magneto
 */
export interface MagnetoUser {
  id: string;
  email: string;
  name: string;
  role: "admin" | "auditeur" | "manager" | "utilisateur" | "responsable_qualite" | "responsable_conformite";
  organizationId: string;
  isActive: boolean;
  lastLoginAt?: Date;
  image?: string;
}

export interface MagnetoSession {
  user: MagnetoUser;
  expires: string;
}

/**
 * Utilitaires pour vérifier les permissions
 */
export const hasPermission = (user: any, permission: string): boolean => {
  if (!user || !user.isActive) return false;

  const rolePermissions = {
    admin: ["*"], // Toutes les permissions
    auditeur: ["audit.create", "audit.read", "audit.update", "report.create", "report.read"],
    manager: ["audit.read", "report.read", "analytics.read", "team.manage"],
    responsable_qualite: ["audit.read", "report.read", "analytics.read", "referential.manage"],
    responsable_conformite: ["audit.read", "report.read", "analytics.read", "action.manage"],
    utilisateur: ["audit.read", "action.update"],
  };

  const userPermissions = rolePermissions[user.role as keyof typeof rolePermissions] || [];
  return userPermissions.includes("*") || userPermissions.includes(permission);
};

/**
 * Hook pour vérifier les permissions
 */
export const usePermissions = () => {
  const { user } = useAuth();

  return {
    canCreateAudit: hasPermission(user, "audit.create"),
    canManageTeam: hasPermission(user, "team.manage"),
    canManageReferentials: hasPermission(user, "referential.manage"),
    canViewAnalytics: hasPermission(user, "analytics.read"),
    canManageActions: hasPermission(user, "action.manage"),
    isAdmin: user?.role === "admin",
    isAuditor: user?.role === "auditeur",
    isManager: user?.role === "manager",
    hasPermission: (permission: string) => hasPermission(user, permission),
  };
};