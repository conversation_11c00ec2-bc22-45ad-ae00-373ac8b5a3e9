'use client';

import Link from 'next/link';
import { usePathname } from 'next/navigation';
import { cn } from '@/lib/utils';
import {
  LayoutDashboard,
  FileText,
  ClipboardList,
  Users,
  Settings,
  Database,
  BarChart3,
  Bell,
  Search,
  Home,
  Building2,
  Shield,
} from 'lucide-react';

/**
 * Configuration des éléments de navigation du sidebar
 */
const navigationItems = [
  {
    title: 'Tableau de bord',
    href: '/',
    icon: LayoutDashboard,
  },
  {
    title: 'Audits',
    href: '/audits',
    icon: ClipboardList,
  },
  {
    title: 'Observations',
    href: '/observations',
    icon: Search,
  },
  {
    title: 'Actions correctives',
    href: '/actions',
    icon: Shield,
  },
  {
    title: 'Rapports',
    href: '/reports',
    icon: FileText,
  },
  {
    title: 'Référentiels',
    href: '/referentials',
    icon: Database,
  },
  {
    title: 'Organisations',
    href: '/organizations',
    icon: Building2,
  },
  {
    title: 'Utilisateurs',
    href: '/users',
    icon: Users,
  },
  {
    title: 'Analytics',
    href: '/analytics',
    icon: BarChart3,
  },
  {
    title: 'Notifications',
    href: '/notifications',
    icon: Bell,
  },
  {
    title: 'Paramètres',
    href: '/settings',
    icon: Settings,
  },
];

interface SidebarProps {
  className?: string;
}

/**
 * Composant Sidebar principal pour la navigation
 * Utilise les couleurs et styles Magneto définis
 */
export function Sidebar({ className }: SidebarProps) {
  const pathname = usePathname();

  return (
    <div className={cn('pb-12 w-64', className)}>
      <div className="space-y-4 py-4">
        {/* Logo et titre */}
        <div className="px-6 py-4">
          <Link href="/" className="flex items-center space-x-3">
            <div className="flex h-8 w-8 items-center justify-center rounded-lg bg-primary">
              <Home className="h-5 w-5 text-white" />
            </div>
            <h1 className="text-xl font-bold text-sidebar-text">Magneto</h1>
          </Link>
        </div>

        {/* Navigation */}
        <div className="px-3">
          <div className="space-y-1">
            {navigationItems.map((item) => {
              const isActive = pathname === item.href || (item.href !== '/' && pathname.startsWith(item.href));
              
              return (
                <Link
                  key={item.href}
                  href={item.href}
                  className={cn(
                    'group flex w-full items-center rounded-md px-3 py-2 text-sm font-medium transition-colors',
                    'text-sidebar-text hover:bg-sidebar-selected hover:text-sidebar-text',
                    isActive && 'bg-sidebar-selected text-sidebar-text font-semibold'
                  )}
                >
                  <item.icon className="mr-3 h-4 w-4 flex-shrink-0" />
                  {item.title}
                </Link>
              );
            })}
          </div>
        </div>
      </div>
    </div>
  );
}
