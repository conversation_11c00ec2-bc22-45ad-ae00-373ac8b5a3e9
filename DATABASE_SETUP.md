# Configuration SQL Server pour Magneto

## Prérequis

1. **SQL Server installé et configuré**
   - SQL Server 2019+ ou SQL Server Express
   - Service SQL Server en cours d'exécution
   - Port 1433 ouvert (par défaut)

2. **Base de données créée**
   ```sql
   CREATE DATABASE magneto_db;
   ```

3. **Utilisateur configuré**
   - Utilisateur `sa` avec mot de passe approprié
   - Ou créer un utilisateur dédié pour Magneto

## Configuration des Variables d'Environnement

Copiez `.env.example` vers `.env` et configurez :

```bash
# Base de données SQL Server
DATABASE_URL="sqlserver://localhost:1433;database=magneto_db;user=sa;password=***************;trustServerCertificate=true;encrypt=true"

# Better Auth
BETTER_AUTH_SECRET="votre-cle-secrete-unique-en-production"
```

## Exécution des Migrations

Une fois SQL Server configuré :

```bash
# 1. Générer le client Prisma
npx prisma generate

# 2. <PERSON><PERSON>er et appliquer la migration initiale
npx prisma migrate dev --name init

# 3. Vérifier la connexion
npx prisma db pull
```

## Structure de Base de Données Créée

- **18 tables** pour le système Magneto
- **Relations avec contraintes appropriées** pour SQL Server
- **Index automatiques** sur les clés primaires et uniques
- **Actions CASCADE/NO ACTION** optimisées

### Tables Principales

1. **Authentification** : users, accounts, sessions, verificationtokens
2. **Organisation** : organizations
3. **Audits** : audits, audit_assignments, referentials, checkpoints
4. **Observations** : observations, actions
5. **Rapports** : reports, report_collaborations, templates
6. **Système** : activity_logs, notifications, system_settings

## Données de Test (Optionnel)

Après migration, vous pouvez ajouter des données de test :

```bash
npx prisma db seed
```

## Vérification

Pour vérifier que tout fonctionne :

```bash
# Tester la connexion
npx prisma db pull

# Voir les tables créées
npx prisma studio
```

---

**Note** : Assurez-vous que SQL Server accepte les connexions TCP/IP et que l'authentification SQL Server est activée.
