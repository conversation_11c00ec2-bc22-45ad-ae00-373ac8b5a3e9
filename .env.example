# Configuration de base
NEXT_PUBLIC_APP_URL=http://localhost:3000
NEXT_PUBLIC_APP_NAME=Magneto

# Base de données SQL Server
DATABASE_URL="sqlserver://localhost:1433;database=magneto_db;user=sa;password=YourPassword123;trustServerCertificate=true;encrypt=true"

# SQL Server Configuration Alternative (pour connection directe)
DB_HOST=localhost
DB_PORT=1433
DB_NAME=magneto_db
DB_USER=sa
DB_PASSWORD=YourPassword123
DB_ENCRYPT=true
DB_TRUST_SERVER_CERTIFICATE=true

# Better Auth Configuration
BETTER_AUTH_SECRET="your-super-secret-key-change-in-production"
BETTER_AUTH_URL=http://localhost:3000

# OAuth Providers (optionnel)
GOOGLE_CLIENT_ID=""
GOOGLE_CLIENT_SECRET=""
MICROSOFT_CLIENT_ID=""
MICROSOFT_CLIENT_SECRET=""

# Email Configuration (pour les notifications)
SMTP_HOST=""
SMTP_PORT=587
SMTP_USER=""
SMTP_PASSWORD=""
EMAIL_FROM=""

# Redis Configuration (pour le cache)
REDIS_URL="redis://localhost:6379"

# File Upload Configuration
NEXT_PUBLIC_MAX_FILE_SIZE=10485760
NEXT_PUBLIC_ALLOWED_FILE_TYPES="image/jpeg,image/png,image/webp,application/pdf,application/vnd.openxmlformats-officedocument.wordprocessingml.document"

# API Keys (pour intégrations futures)
OPENAI_API_KEY=""
AZURE_STORAGE_CONNECTION_STRING=""

# Monitoring et Analytics
SENTRY_DSN=""
GOOGLE_ANALYTICS_ID=""

# Environment
NODE_ENV=development
