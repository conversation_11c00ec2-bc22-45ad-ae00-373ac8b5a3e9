import { z } from "zod";
import { emailSchema, passwordSchema, nameSchema } from "./common";

/**
 * Schémas de validation pour l'authentification
 */

// Connexion
export const loginSchema = z.object({
  email: emailSchema,
  password: z.string().min(1, "Le mot de passe est obligatoire"),
  rememberMe: z.boolean().optional().default(false),
});

// Inscription
export const registerSchema = z.object({
  email: emailSchema,
  password: passwordSchema,
  confirmPassword: z.string(),
  name: nameSchema,
  organizationName: z.string().min(2, "Le nom de l'organisation doit contenir au moins 2 caractères"),
  acceptTerms: z.boolean().refine((val) => val === true, {
    message: "Vous devez accepter les conditions d'utilisation",
  }),
}).refine((data) => data.password === data.confirmPassword, {
  message: "Les mots de passe ne correspondent pas",
  path: ["confirmPassword"],
});

// Mot de passe oublié
export const forgotPasswordSchema = z.object({
  email: emailSchema,
});

// Réinitialisation du mot de passe
export const resetPasswordSchema = z.object({
  token: z.string().min(1, "Token de réinitialisation manquant"),
  password: passwordSchema,
  confirmPassword: z.string(),
}).refine((data) => data.password === data.confirmPassword, {
  message: "Les mots de passe ne correspondent pas",
  path: ["confirmPassword"],
});

// Changement de mot de passe
export const changePasswordSchema = z.object({
  currentPassword: z.string().min(1, "Le mot de passe actuel est obligatoire"),
  newPassword: passwordSchema,
  confirmNewPassword: z.string(),
}).refine((data) => data.newPassword === data.confirmNewPassword, {
  message: "Les nouveaux mots de passe ne correspondent pas",
  path: ["confirmNewPassword"],
}).refine((data) => data.currentPassword !== data.newPassword, {
  message: "Le nouveau mot de passe doit être différent de l'ancien",
  path: ["newPassword"],
});

// Vérification 2FA
export const verify2FASchema = z.object({
  code: z.string()
    .length(6, "Le code doit contenir exactement 6 chiffres")
    .regex(/^\d+$/, "Le code ne peut contenir que des chiffres"),
});

// Configuration 2FA
export const setup2FASchema = z.object({
  secret: z.string().min(1, "Secret 2FA manquant"),
  code: z.string()
    .length(6, "Le code doit contenir exactement 6 chiffres")
    .regex(/^\d+$/, "Le code ne peut contenir que des chiffres"),
});

/**
 * Types TypeScript générés à partir des schémas
 */
export type LoginInput = z.infer<typeof loginSchema>;
export type RegisterInput = z.infer<typeof registerSchema>;
export type ForgotPasswordInput = z.infer<typeof forgotPasswordSchema>;
export type ResetPasswordInput = z.infer<typeof resetPasswordSchema>;
export type ChangePasswordInput = z.infer<typeof changePasswordSchema>;
export type Verify2FAInput = z.infer<typeof verify2FASchema>;
export type Setup2FAInput = z.infer<typeof setup2FASchema>;
