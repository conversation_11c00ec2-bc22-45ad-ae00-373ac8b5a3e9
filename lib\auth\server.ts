import { auth } from '@/lib/auth/config';
import { headers } from 'next/headers';
import { redirect } from 'next/navigation';

/**
 * Types pour les vérifications d'authentification côté serveur
 */
export interface ServerAuthResult {
  user: {
    id: string;
    email: string;
    name: string;
    role: string;
    organizationId: string;
    isActive: boolean;
    lastLoginAt?: Date;
  } | null;
  isAuthenticated: boolean;
}

/**
 * Obtient la session utilisateur côté serveur
 * Compatible avec Server Components et API Routes
 */
export async function getServerSession(): Promise<ServerAuthResult> {
  try {
    const session = await auth.api.getSession({
      headers: headers()
    });

    if (!session || !session.user) {
      return {
        user: null,
        isAuthenticated: false
      };
    }

    return {
      user: session.user,
      isAuthenticated: true
    };
  } catch (error) {
    console.error('Erreur lors de la récupération de la session:', error);
    return {
      user: null,
      isAuthenticated: false
    };
  }
}

/**
 * Vérifie que l'utilisateur est connecté, sinon redirige vers la connexion
 */
export async function requireAuth(callbackUrl?: string): Promise<NonNullable<ServerAuthResult['user']>> {
  const { user, isAuthenticated } = await getServerSession();

  if (!isAuthenticated || !user) {
    const redirectUrl = callbackUrl || '/auth/signin';
    redirect(redirectUrl);
  }

  return user;
}

/**
 * Vérifie que l'utilisateur est connecté et actif
 */
export async function requireActiveUser(callbackUrl?: string): Promise<NonNullable<ServerAuthResult['user']>> {
  const user = await requireAuth(callbackUrl);

  if (!user?.isActive) {
    redirect('/auth/account-disabled');
  }

  return user;
}

/**
 * Vérifie que l'utilisateur a un rôle spécifique
 */
export async function requireRole(
  requiredRoles: string[],
  unauthorizedRedirect: string = '/unauthorized'
): Promise<NonNullable<ServerAuthResult['user']>> {
  const user = await requireActiveUser();

  if (!user || !requiredRoles.includes(user.role)) {
    redirect(unauthorizedRedirect);
  }

  return user;
}

/**
 * Vérifie que l'utilisateur est administrateur
 */
export async function requireAdmin(): Promise<NonNullable<ServerAuthResult['user']>> {
  return requireRole(['admin', 'super_admin']);
}

/**
 * Vérifie que l'utilisateur est auditeur ou plus
 */
export async function requireAuditor(): Promise<NonNullable<ServerAuthResult['user']>> {
  return requireRole(['auditeur', 'manager', 'admin', 'super_admin']);
}

/**
 * Vérifie que l'utilisateur est manager ou plus
 */
export async function requireManager(): Promise<NonNullable<ServerAuthResult['user']>> {
  return requireRole(['manager', 'admin', 'super_admin']);
}

/**
 * Vérifie les permissions sans redirection (pour usage conditionnel)
 */
export async function checkPermissions(): Promise<{
  user: ServerAuthResult['user'];
  isAuthenticated: boolean;
  isActive: boolean;
  isAdmin: boolean;
  isAuditor: boolean;
  isManager: boolean;
}> {
  const { user, isAuthenticated } = await getServerSession();

  if (!isAuthenticated || !user) {
    return {
      user: null,
      isAuthenticated: false,
      isActive: false,
      isAdmin: false,
      isAuditor: false,
      isManager: false
    };
  }

  const adminRoles = ['admin', 'super_admin'];
  const auditorRoles = ['auditeur', 'manager', 'admin', 'super_admin'];
  const managerRoles = ['manager', 'admin', 'super_admin'];

  return {
    user,
    isAuthenticated: true,
    isActive: user.isActive,
    isAdmin: adminRoles.includes(user.role),
    isAuditor: auditorRoles.includes(user.role),
    isManager: managerRoles.includes(user.role)
  };
}

/**
 * Helper pour les API Routes - vérifie l'authentification et retourne une réponse JSON appropriée
 */
export async function verifyApiAuth(): Promise<{
  user: ServerAuthResult['user'];
  error?: Response;
}> {
  const { user, isAuthenticated } = await getServerSession();

  if (!isAuthenticated || !user) {
    return {
      user: null,
      error: Response.json({ error: 'Non autorisé' }, { status: 401 })
    };
  }

  if (!user.isActive) {
    return {
      user: null,
      error: Response.json({ error: 'Compte désactivé' }, { status: 403 })
    };
  }

  return { user };
}

/**
 * Helper pour les API Routes admin
 */
export async function verifyApiAdmin(): Promise<{
  user: ServerAuthResult['user'];
  error?: Response;
}> {
  const { user, error } = await verifyApiAuth();

  if (error) return { user, error };

  if (!user || !['admin', 'super_admin'].includes(user.role)) {
    return {
      user: null,
      error: Response.json(
        { error: 'Privilèges administrateur requis' },
        { status: 403 }
      )
    };
  }

  return { user };
}
