{"extends": ["next/core-web-vitals", "@typescript-eslint/recommended", "prettier"], "parser": "@typescript-eslint/parser", "plugins": ["@typescript-eslint", "prettier"], "rules": {"prettier/prettier": "error", "@typescript-eslint/no-unused-vars": "error", "@typescript-eslint/no-explicit-any": "warn", "@typescript-eslint/no-non-null-assertion": "warn", "prefer-const": "error", "no-var": "error"}, "ignorePatterns": ["node_modules/", ".next/", "out/", "dist/"]}