const { PrismaClient } = require('@prisma/client');

/**
 * Script de vérification de la base de données
 * Vérifie que toutes les tables ont été créées correctement
 */
async function verifyDatabase() {
  const prisma = new PrismaClient();
  
  try {
    console.log('🔍 Vérification de la base de données...\n');
    
    // Test de connexion
    await prisma.$connect();
    console.log('✅ Connexion Prisma réussie');
      // Vérification des tables principales du schéma Magneto
    const tables = [
      'users',
      'accounts',
      'sessions',
      'verificationtokens',
      'organizations',
      'audits',
      'audit_assignments',
      'referentials',
      'checkpoints',
      'observations',
      'actions',
      'reports',
      'report_collaborations',
      'templates',
      'activity_logs',
      'notifications',
      'system_settings'
    ];
    
    console.log('\n📊 Vérification des tables:');
    
    for (const table of tables) {
      try {
        // Utilisation de $queryRaw pour vérifier l'existence des tables
        const result = await prisma.$queryRaw`
          SELECT COUNT(*) as count 
          FROM INFORMATION_SCHEMA.TABLES 
          WHERE TABLE_NAME = ${table}
        `;
        
        if (result[0].count > 0) {
          console.log(`   ✅ ${table}`);
        } else {
          console.log(`   ❌ ${table} - Table manquante`);
        }
      } catch (error) {
        console.log(`   ⚠️  ${table} - Erreur: ${error.message}`);
      }
    }
    
    // Test d'opérations CRUD basiques
    console.log('\n🧪 Test d\'opérations CRUD...');
    
    try {
      // Compter les enregistrements existants
      const userCount = await prisma.user.count();
      const orgCount = await prisma.organization.count();
      
      console.log(`   📈 Users: ${userCount}`);
      console.log(`   📈 Organizations: ${orgCount}`);
      
      console.log('\n✨ Base de données prête pour le développement!');
      
    } catch (error) {
      console.log(`   ❌ Erreur CRUD: ${error.message}`);
    }
    
  } catch (error) {
    console.error('❌ Erreur de vérification:', error.message);
  } finally {
    await prisma.$disconnect();
  }
}

// Exécution du script
verifyDatabase()
  .then(() => {
    console.log('\n🎯 Vérification terminée');
    process.exit(0);
  })
  .catch((error) => {
    console.error('💥 Erreur fatale:', error);
    process.exit(1);
  });
