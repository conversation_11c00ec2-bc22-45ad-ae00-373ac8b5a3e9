@tailwind base;
@tailwind components;
@tailwind utilities;

@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Poppins:wght@300;400;500;600;700&family=Roboto:wght@300;400;500;700&display=swap');

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 0 0% 3.9%;
    --card: 0 0% 100%;
    --card-foreground: 0 0% 3.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 0 0% 3.9%;
    --primary: 216 56% 31%;
    --primary-foreground: 0 0% 98%;
    --secondary: 0 0% 96.1%;
    --secondary-foreground: 0 0% 9%;
    --muted: 0 0% 96.1%;
    --muted-foreground: 0 0% 45.1%;
    --accent: 0 0% 96.1%;
    --accent-foreground: 0 0% 9%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 0 0% 98%;
    --border: 0 0% 89.8%;
    --input: 0 0% 89.8%;
    --ring: 216 56% 31%;
    --radius: 0.5rem;
  }

  .dark {
    --background: 0 0% 3.9%;
    --foreground: 0 0% 98%;
    --card: 0 0% 3.9%;
    --card-foreground: 0 0% 98%;
    --popover: 0 0% 3.9%;
    --popover-foreground: 0 0% 98%;
    --primary: 216 56% 31%;
    --primary-foreground: 0 0% 98%;
    --secondary: 0 0% 14.9%;
    --secondary-foreground: 0 0% 98%;
    --muted: 0 0% 14.9%;
    --muted-foreground: 0 0% 63.9%;
    --accent: 0 0% 14.9%;
    --accent-foreground: 0 0% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 0 0% 98%;
    --border: 0 0% 14.9%;
    --input: 0 0% 14.9%;
    --ring: 216 56% 31%;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
    font-family: 'Inter', 'Poppins', 'Roboto', sans-serif;
  }
}

@layer components {
  /* Sidebar styles - selon les spécifications Magneto */
  .sidebar {
    @apply bg-magneto-sidebar text-magneto-text-sidebar;
  }
  
  .sidebar-item {
    @apply hover:bg-magneto-selected transition-colors duration-200 rounded-md px-3 py-2 mx-2;
  }
  
  .sidebar-item-active {
    @apply bg-magneto-selected text-white;
  }
  
  /* Navbar styles */
  .navbar {
    @apply bg-magneto-navbar text-white shadow-sm;
  }
  
  /* Workspace styles */
  .workspace {
    @apply bg-magneto-workspace min-h-screen;
  }
  
  /* Button styles selon spécifications */
  .btn-primary {
    @apply bg-magneto-primary text-white hover:bg-magneto-primary/90 transition-colors duration-200 font-medium px-4 py-2 rounded-md;
  }
  
  .btn-secondary {
    @apply bg-gray-200 text-gray-900 hover:bg-gray-300 transition-colors duration-200 font-medium px-4 py-2 rounded-md;
  }
  
  .btn-danger {
    @apply bg-magneto-danger text-white hover:bg-magneto-danger/90 transition-colors duration-200 font-medium px-4 py-2 rounded-md;
  }
  
  /* Input styles selon spécifications */
  .input-default {
    @apply border-gray-300 text-black placeholder-gray-400 focus:border-magneto-primary focus:ring-magneto-primary/20 rounded-md px-3 py-2;
  }
  
  /* Card styles */
  .card-default {
    @apply bg-white border border-gray-200 rounded-lg shadow-sm p-6;
  }
  
  /* Table styles */
  .table-default {
    @apply w-full border-collapse bg-white;
  }
  
  .table-header {
    @apply bg-gray-50 border-b border-gray-200;
  }
  
  .table-cell {
    @apply px-4 py-3 text-sm border-b border-gray-100;
  }
  
  /* Form styles */
  .form-group {
    @apply space-y-2;
  }
  
  .form-error {
    @apply text-magneto-danger text-sm mt-1;
  }
  
  .form-success {
    @apply text-green-600 text-sm mt-1;
  }
  
  /* Badge styles */
  .badge-primary {
    @apply bg-magneto-primary text-white px-2 py-1 rounded-full text-xs font-medium;
  }
  
  .badge-success {
    @apply bg-green-100 text-green-800 px-2 py-1 rounded-full text-xs font-medium;
  }
  
  .badge-warning {
    @apply bg-yellow-100 text-yellow-800 px-2 py-1 rounded-full text-xs font-medium;
  }
  
  .badge-danger {
    @apply bg-red-100 text-red-800 px-2 py-1 rounded-full text-xs font-medium;
  }
  
  /* Status indicators */
  .status-dot {
    @apply w-2 h-2 rounded-full inline-block mr-2;
  }
  
  .status-active {
    @apply bg-green-500;
  }
  
  .status-inactive {
    @apply bg-gray-400;
  }
  
  .status-pending {
    @apply bg-yellow-500;
  }
  
  .status-error {
    @apply bg-red-500;
  }
  
  /* Animation utilities */
  .fade-in {
    @apply transition-opacity duration-300 ease-in-out;
  }
  
  .slide-in {
    @apply transition-transform duration-300 ease-in-out;
  }
  
  /* Layout utilities */
  .container-magneto {
    @apply max-w-7xl mx-auto px-4 sm:px-6 lg:px-8;
  }
  
  .section-padding {
    @apply py-8 lg:py-12;
  }
  
  /* Typography utilities */
  .heading-1 {
    @apply text-3xl lg:text-4xl font-bold text-black font-poppins;
  }
  
  .heading-2 {
    @apply text-2xl lg:text-3xl font-bold text-black font-poppins;
  }
  
  .heading-3 {
    @apply text-xl lg:text-2xl font-semibold text-black font-poppins;
  }
  
  .body-large {
    @apply text-lg text-gray-700 font-inter;
  }
  
  .body-medium {
    @apply text-base text-gray-700 font-inter;
  }
  
  .body-small {
    @apply text-sm text-gray-600 font-inter;
  }
  
  /* Label styles */
  .label-default {
    @apply text-black font-medium text-sm font-inter;
  }
  
  /* Title styles */
  .title-default {
    @apply text-black font-semibold text-lg font-poppins;
  }
  
  /* Focus states */
  .focus-ring {
    @apply focus:outline-none focus:ring-2 focus:ring-magneto-primary/20 focus:border-magneto-primary;
  }
}
