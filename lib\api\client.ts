import { QueryClient } from "@tanstack/react-query";

/**
 * Configuration du client React Query pour Magneto
 */
export const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      // Stale time de 5 minutes
      staleTime: 5 * 60 * 1000,
      // Cache time de 10 minutes
      gcTime: 10 * 60 * 1000,
      // Retry automatique en cas d'échec
      retry: (failureCount, error: any) => {
        // Ne pas retry les erreurs 404 ou 401
        if (error?.status === 404 || error?.status === 401) {
          return false;
        }
        // Retry jusqu'à 2 fois pour les autres erreurs
        return failureCount < 2;
      },
      // Refetch automatique au focus de la fenêtre
      refetchOnWindowFocus: false,
      // Refetch automatique à la reconnexion
      refetchOnReconnect: true,
    },
    mutations: {
      // Retry automatique pour les mutations en cas d'erreur réseau
      retry: (failureCount, error: any) => {
        if (error?.status >= 400 && error?.status < 500) {
          return false; // Erreurs client, ne pas retry
        }
        return failureCount < 1; // Retry une fois pour les erreurs serveur
      },
    },
  },
});

/**
 * Types pour les réponses API
 */
export interface ApiResponse<T = any> {
  data: T;
  success: boolean;
  message?: string;
  errors?: Record<string, string[]>;
  meta?: {
    pagination?: {
      page: number;
      limit: number;
      total: number;
      totalPages: number;
    };
  };
}

export interface ApiError {
  status: number;
  message: string;
  errors?: Record<string, string[]>;
}

/**
 * Client API personnalisé avec gestion d'erreurs
 */
class ApiClient {
  private baseURL: string;

  constructor(baseURL: string = "/api") {
    this.baseURL = baseURL;
  }

  private async request<T>(
    endpoint: string,
    options: RequestInit = {}
  ): Promise<ApiResponse<T>> {
    const url = `${this.baseURL}${endpoint}`;
    
    const config: RequestInit = {
      headers: {
        "Content-Type": "application/json",
        ...options.headers,
      },
      ...options,
    };

    try {
      const response = await fetch(url, config);
      
      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw {
          status: response.status,
          message: errorData.message || `HTTP ${response.status}`,
          errors: errorData.errors,
        } as ApiError;
      }

      return await response.json();
    } catch (error) {
      if (error && typeof error === "object" && "status" in error) {
        throw error;
      }
      
      // Erreur réseau ou autre
      throw {
        status: 0,
        message: "Erreur de connexion",
      } as ApiError;
    }
  }

  async get<T>(endpoint: string, params?: Record<string, any>): Promise<ApiResponse<T>> {
    const url = params 
      ? `${endpoint}?${new URLSearchParams(params).toString()}`
      : endpoint;
    
    return this.request<T>(url, { method: "GET" });
  }

  async post<T>(endpoint: string, data?: any): Promise<ApiResponse<T>> {
    return this.request<T>(endpoint, {
      method: "POST",
      body: data ? JSON.stringify(data) : undefined,
    });
  }

  async put<T>(endpoint: string, data?: any): Promise<ApiResponse<T>> {
    return this.request<T>(endpoint, {
      method: "PUT",
      body: data ? JSON.stringify(data) : undefined,
    });
  }

  async patch<T>(endpoint: string, data?: any): Promise<ApiResponse<T>> {
    return this.request<T>(endpoint, {
      method: "PATCH",
      body: data ? JSON.stringify(data) : undefined,
    });
  }

  async delete<T>(endpoint: string): Promise<ApiResponse<T>> {
    return this.request<T>(endpoint, { method: "DELETE" });
  }

  // Upload de fichiers
  async upload<T>(endpoint: string, formData: FormData): Promise<ApiResponse<T>> {
    return this.request<T>(endpoint, {
      method: "POST",
      body: formData,
      headers: {}, // Pas de Content-Type pour FormData
    });
  }
}

export const apiClient = new ApiClient();

/**
 * Hook personnalisé pour les erreurs API
 */
export function useApiError() {
  return {
    formatError: (error: ApiError) => {
      if (error.errors) {
        // Erreurs de validation
        return Object.entries(error.errors)
          .map(([field, messages]) => `${field}: ${messages.join(", ")}`)
          .join("; ");
      }
      return error.message;
    },
    
    isValidationError: (error: ApiError) => {
      return error.status === 422 && !!error.errors;
    },
    
    isAuthError: (error: ApiError) => {
      return error.status === 401 || error.status === 403;
    },
    
    isNotFoundError: (error: ApiError) => {
      return error.status === 404;
    },
  };
}
