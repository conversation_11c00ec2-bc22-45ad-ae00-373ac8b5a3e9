'use client';

import { cn } from '@/lib/utils';
import { Sidebar } from './sidebar';
import { Navbar } from './navbar';
import { useUIStore } from '@/lib/stores/ui-store';

interface MainLayoutProps {
  children: React.ReactNode;
  className?: string;
}

/**
 * Layout principal de l'application Magneto
 * Intègre la sidebar, la navbar et l'espace de travail
 */
export function MainLayout({ children, className }: MainLayoutProps) {
  const { sidebarOpen } = useUIStore();

  return (
    <div className="h-screen flex overflow-hidden bg-workspace-bg">
      {/* Sidebar */}
      <div
        className={cn(
          'fixed inset-y-0 left-0 z-40 w-64 transform bg-sidebar-bg transition-transform duration-300 ease-in-out md:relative md:translate-x-0',
          sidebarOpen ? 'translate-x-0' : '-translate-x-full'
        )}
      >
        <Sidebar />
      </div>

      {/* Overlay pour mobile */}
      {sidebarOpen && (
        <div
          className="fixed inset-0 z-30 bg-black bg-opacity-50 md:hidden"
          onClick={() => useUIStore.getState().setSidebarOpen(false)}
        />
      )}

      {/* Main content */}
      <div className="flex flex-1 flex-col overflow-hidden">
        {/* Navbar */}
        <Navbar />

        {/* Page content */}
        <main className={cn('flex-1 overflow-y-auto p-6', className)}>
          <div className="mx-auto max-w-7xl">
            {children}
          </div>
        </main>
      </div>
    </div>
  );
}
