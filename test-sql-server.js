/**
 * Script de test et création de base de données SQL Server
 */

const sql = require('mssql');
require('dotenv').config();

async function testConnection() {
  console.log('🔄 Test de connexion SQL Server...\n');
  
  // Configuration de connexion sans spécifier la base de données
  const config = {
    server: 'localhost',
    port: 1433,
    user: 'magneto',
    password: '@Bertys01',
    options: {
      encrypt: true,
      trustServerCertificate: true
    }
  };

  try {
    console.log('📡 Connexion au serveur SQL Server...');
    const pool = await sql.connect(config);
    console.log('✅ Connexion au serveur réussie !');
    
    // Vérifier si la base de données existe
    console.log('\n🔍 Vérification de l\'existence de la base de données magneto_db...');
    const checkDb = await pool.request()
      .query("SELECT name FROM sys.databases WHERE name = 'magneto_db'");
    
    if (checkDb.recordset.length === 0) {
      console.log('📋 Base de données magneto_db non trouvée');
      console.log('🔨 Création de la base de données magneto_db...');
      
      await pool.request().query('CREATE DATABASE magneto_db');
      console.log('✅ Base de données magneto_db créée avec succès !');
    } else {
      console.log('✅ Base de données magneto_db existe déjà');
    }
    
    // Test de connexion à la base de données spécifique
    await pool.close();
    
    const dbConfig = {
      ...config,
      database: 'magneto_db'
    };
    
    console.log('\n🔗 Test de connexion à magneto_db...');
    const dbPool = await sql.connect(dbConfig);
    
    const testQuery = await dbPool.request().query('SELECT 1 as test');
    console.log('✅ Connexion à magneto_db réussie !');
    console.log(`📊 Test query result: ${testQuery.recordset[0].test}`);
    
    await dbPool.close();
    
    console.log('\n🎯 Configuration SQL Server validée !');
    console.log('✨ Vous pouvez maintenant exécuter les migrations Prisma');
    
  } catch (error) {
    console.error('\n❌ Erreur de connexion SQL Server:');
    console.error(error.message);
    
    console.log('\n💡 Solutions possibles:');
    console.log('   - Vérifier que SQL Server est démarré');
    console.log('   - Vérifier les credentials (user: magneto, password: @Bertys01)');
    console.log('   - Créer l\'utilisateur "magneto" s\'il n\'existe pas');
    console.log('   - Vérifier que le port 1433 est ouvert');
    console.log('   - Activer l\'authentification SQL Server');
  }
}

testConnection();
