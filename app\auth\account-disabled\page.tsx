import Link from 'next/link';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { UserX, Mail, ArrowLeft } from 'lucide-react';

export default function AccountDisabledPage() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 to-slate-100 flex items-center justify-center p-4">
      <Card className="w-full max-w-md text-center border-0 shadow-xl">
        <CardHeader className="space-y-4">
          <div className="w-16 h-16 bg-orange-100 rounded-full flex items-center justify-center mx-auto">
            <UserX className="h-8 w-8 text-orange-600" />
          </div>
          <CardTitle className="text-2xl font-bold text-gray-900">
            Compte désactivé
          </CardTitle>
          <CardDescription className="text-gray-600">
            Votre compte a été temporairement désactivé.
          </CardDescription>
        </CardHeader>
        
        <CardContent className="space-y-4">
          <div className="text-sm text-gray-600 space-y-2">
            <p>
              Votre compte Magneto a été désactivé par un administrateur.
            </p>
            <p>
              Pour réactiver votre compte, veuillez contacter votre administrateur système ou l'équipe support.
            </p>
          </div>
          
          <div className="flex flex-col gap-2 sm:flex-row sm:justify-center">
            <Button variant="outline" asChild className="flex items-center gap-2">
              <Link href="/auth/signin">
                <ArrowLeft className="h-4 w-4" />
                Retour à la connexion
              </Link>
            </Button>
            
            <Button asChild className="flex items-center gap-2 bg-[#2E427D] hover:bg-[#1e2d5c]">
              <Link href="mailto:<EMAIL>?subject=Réactivation de compte">
                <Mail className="h-4 w-4" />
                Contacter le support
              </Link>
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
