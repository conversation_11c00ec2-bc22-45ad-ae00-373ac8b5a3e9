# Système d'Authentification Magneto

## Vue d'ensemble

Le système d'authentification de Magneto utilise **Better Auth**, une solution moderne et sécurisée pour Next.js qui offre :

- Authentification email/mot de passe
- Connexion sociale (Google, Microsoft)
- Gestion de session sécurisée
- Protection des routes avec middleware
- Vérification d'email
- Réinitialisation de mot de passe
- Gestion des rôles et permissions

## Architecture

### Composants principaux

1. **Configuration serveur** (`lib/auth/config.ts`) : Configuration Better Auth
2. **Client côté client** (`lib/auth/client.ts`) : Hooks et utilitaires React
3. **Middleware** (`middleware.ts`) : Protection des routes et vérification des permissions
4. **API Routes** (`app/api/auth/[...all]/route.ts`) : Endpoints d'authentification
5. **Pages d'authentification** : Connexion, inscription, erreurs

## Configuration

### Variables d'environnement requises

```env
DATABASE_URL="sqlserver://..."
BETTER_AUTH_SECRET="your-secret-key"
BETTER_AUTH_URL="http://localhost:3000"
NEXT_PUBLIC_BASE_URL="http://localhost:3000"

# Optionnel : Connexion sociale
GOOGLE_CLIENT_ID="your-google-client-id"
GOOGLE_CLIENT_SECRET="your-google-client-secret"
MICROSOFT_CLIENT_ID="your-microsoft-client-id"
MICROSOFT_CLIENT_SECRET="your-microsoft-client-secret"
```

### Modèle de données

Le système utilise les modèles Prisma standard de Better Auth :
- `User` : Informations utilisateur avec champs personnalisés Magneto
- `Account` : Comptes de connexion sociale
- `Session` : Sessions utilisateur
- `VerificationToken` : Tokens de vérification

#### Champs personnalisés Magneto

```typescript
{
  role: string; // admin, auditeur, manager, etc.
  organizationId: string; // Lien vers l'organisation
  isActive: boolean; // Statut du compte
  lastLoginAt: DateTime; // Dernière connexion
}
```

## Utilisation

### 1. Côté client - Hooks

```typescript
import { useAuthState } from '@/lib/auth/client';

function MonComposant() {
  const { user, isAuthenticated, isLoading } = useAuthState();
  
  if (isLoading) return <div>Chargement...</div>;
  if (!isAuthenticated) return <div>Non connecté</div>;
  
  return <div>Bonjour {user.name}!</div>;
}
```

### 2. Côté client - Actions

```typescript
import { authClient } from '@/lib/auth/client';

// Connexion
await authClient.signIn.email({
  email: '<EMAIL>',
  password: 'password123'
});

// Inscription
await authClient.signUp.email({
  email: '<EMAIL>',
  password: 'password123',
  name: 'John Doe',
  organizationId: 'org-id',
  role: 'utilisateur'
});

// Déconnexion
await authClient.signOut();
```

### 3. Côté serveur - Vérification de session

```typescript
import { auth } from '@/lib/auth/config';
import { headers } from 'next/headers';

async function MaPageServeur() {
  const session = await auth.api.getSession({
    headers: headers()
  });
  
  if (!session) {
    redirect('/auth/signin');
  }
  
  return <div>Utilisateur: {session.user.name}</div>;
}
```

## Protection des routes

### Middleware automatique

Le middleware protège automatiquement toutes les routes sauf :
- Routes publiques définies dans `publicRoutes`
- API publiques définies dans `publicApiRoutes`

### Routes protégées par rôle

Certaines routes nécessitent des rôles spécifiques :

```typescript
// Routes administrateur
const adminRoutes = [
  '/admin',
  '/settings/system',
  '/users/management'
];
```

### Vérification des permissions

```typescript
import { checkPermission, isAdmin, isAuditor } from '@/lib/auth/client';

// Vérification générale
const canAccess = checkPermission(user.role, ['admin', 'manager']);

// Vérifications spécifiques
const isUserAdmin = isAdmin(user.role);
const isUserAuditor = isAuditor(user.role);
```

## Rôles utilisateur

### Hiérarchie des rôles

1. **super_admin** : Accès complet système
2. **admin** : Administration organisation
3. **manager** : Gestion d'équipe et audits
4. **auditeur** : Réalisation d'audits
5. **responsable_qualite** : Gestion qualité
6. **responsable_conformite** : Gestion conformité
7. **utilisateur** : Accès de base

### Permissions par rôle

| Fonctionnalité | Utilisateur | Auditeur | Manager | Admin | Super Admin |
|----------------|-------------|----------|---------|-------|-------------|
| Voir audits | ✅ | ✅ | ✅ | ✅ | ✅ |
| Créer audits | ❌ | ✅ | ✅ | ✅ | ✅ |
| Assigner auditeurs | ❌ | ❌ | ✅ | ✅ | ✅ |
| Gérer utilisateurs | ❌ | ❌ | ❌ | ✅ | ✅ |
| Config système | ❌ | ❌ | ❌ | ❌ | ✅ |

## Pages d'authentification

### 1. Connexion (`/auth/signin`)

Fonctionnalités :
- Formulaire email/mot de passe avec validation
- Connexion sociale (Google, Microsoft)
- Lien vers mot de passe oublié
- Redirection après connexion

### 2. Inscription (`/auth/signup`)

Fonctionnalités :
- Formulaire complet avec validation robuste
- Indicateur de force du mot de passe
- Confirmation de mot de passe
- Sélection d'organisation
- Conditions d'utilisation

### 3. Pages d'erreur

- **Non autorisé** (`/unauthorized`) : Accès refusé
- **Compte désactivé** (`/auth/account-disabled`) : Compte inactif

## Sécurité

### Mesures de protection

1. **Validation côté client et serveur**
2. **Hashage sécurisé des mots de passe** (bcrypt)
3. **Sessions sécurisées** avec cookies httpOnly
4. **Protection CSRF** intégrée
5. **Validation des entrées** avec Zod
6. **Rate limiting** (à implémenter)

### Bonnes pratiques implémentées

- Mots de passe complexes requis
- Vérification d'email obligatoire
- Sessions avec expiration
- Audit des connexions
- Protection contre les attaques par force brute

## API Endpoints

Better Auth expose automatiquement ces endpoints :

```
POST /api/auth/sign-in          # Connexion
POST /api/auth/sign-up          # Inscription
POST /api/auth/sign-out         # Déconnexion
GET  /api/auth/session          # Session actuelle
POST /api/auth/forgot-password  # Mot de passe oublié
POST /api/auth/reset-password   # Réinitialisation
GET  /api/auth/verify-email     # Vérification email
```

## Intégration avec le système d'audit

Toutes les actions d'authentification sont automatiquement auditées :

```typescript
// Connexion utilisateur
await createAuditLog(
  AuditActions.LOGIN,
  AuditEntities.USER,
  userId,
  'Utilisateur connecté',
  { ipAddress, userAgent }
);

// Déconnexion utilisateur
await createAuditLog(
  AuditActions.LOGOUT,
  AuditEntities.USER,
  userId,
  'Utilisateur déconnecté'
);
```

## Déploiement

### Variables de production

```env
BETTER_AUTH_SECRET="secure-production-secret"
BETTER_AUTH_URL="https://votre-domaine.com"
NEXT_PUBLIC_BASE_URL="https://votre-domaine.com"
```

### Configuration SSL

En production, s'assurer que :
- HTTPS est activé
- Cookies sécurisés sont utilisés
- Headers de sécurité sont configurés

## Développement futur

### Fonctionnalités à ajouter

- [ ] Authentification à deux facteurs (2FA)
- [ ] Connexion SSO (SAML, OIDC)
- [ ] Rate limiting avancé
- [ ] Audit de sécurité détaillé
- [ ] Gestion des sessions multiples
- [ ] Notifications de sécurité

### Améliorations UX

- [ ] Connexion par QR code
- [ ] Biométrie (WebAuthn)
- [ ] Mode hors ligne
- [ ] Thème sombre pour l'authentification
