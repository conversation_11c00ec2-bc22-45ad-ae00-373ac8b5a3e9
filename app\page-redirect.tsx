import { redirect } from 'next/navigation';
import { auth } from '@/lib/auth/config';
import { headers } from 'next/headers';

/**
 * Page d'accueil qui redirige vers le tableau de bord si l'utilisateur est connecté,
 * sinon vers la page de connexion
 */
export default async function HomePage() {
  // Vérifier la session côté serveur
  const session = await auth.api.getSession({
    headers: headers()
  });

  // Rediriger selon l'état de connexion
  if (session?.user) {
    redirect('/dashboard');
  } else {
    redirect('/auth/signin');
  }
}
