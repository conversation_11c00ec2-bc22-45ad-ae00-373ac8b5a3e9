import { z } from "zod";

/**
 * Schémas de validation communs
 */

// Email validation
export const emailSchema = z
  .string()
  .min(1, "L'email est obligatoire")
  .email("Format d'email invalide");

// Password validation
export const passwordSchema = z
  .string()
  .min(8, "Le mot de passe doit contenir au moins 8 caractères")
  .regex(/[A-Z]/, "Le mot de passe doit contenir au moins une majuscule")
  .regex(/[a-z]/, "Le mot de passe doit contenir au moins une minuscule")
  .regex(/[0-9]/, "Le mot de passe doit contenir au moins un chiffre")
  .regex(/[^A-Za-z0-9]/, "Le mot de passe doit contenir au moins un caractère spécial");

// Name validation
export const nameSchema = z
  .string()
  .min(2, "Le nom doit contenir au moins 2 caractères")
  .max(50, "Le nom ne peut pas dépasser 50 caractères")
  .regex(/^[a-zA-ZÀ-ÿ\s-']+$/, "Le nom ne peut contenir que des lettres, espaces, tirets et apostrophes");

// Phone validation (format français)
export const phoneSchema = z
  .string()
  .regex(/^(?:(?:\+|00)33|0)\s*[1-9](?:[\s.-]*\d{2}){4}$/, "Format de téléphone invalide");

// URL validation
export const urlSchema = z
  .string()
  .url("URL invalide")
  .optional()
  .or(z.literal(""));

// File validation
export const fileSchema = z.object({
  name: z.string(),
  size: z.number().max(10 * 1024 * 1024, "Le fichier ne peut pas dépasser 10MB"),
  type: z.string(),
});

// Date validation
export const futureDateSchema = z
  .date()
  .refine((date) => date > new Date(), "La date doit être dans le futur");

export const pastDateSchema = z
  .date()
  .refine((date) => date < new Date(), "La date doit être dans le passé");

// Pagination schema
export const paginationSchema = z.object({
  page: z.number().min(1).default(1),
  limit: z.number().min(1).max(100).default(10),
  sortBy: z.string().optional(),
  sortOrder: z.enum(["asc", "desc"]).default("desc"),
});

/**
 * Utilitaires de validation
 */

// Custom error map pour les messages en français
export const frenchErrorMap: z.ZodErrorMap = (issue, ctx) => {
  switch (issue.code) {
    case z.ZodIssueCode.invalid_type:
      if (issue.expected === "string") {
        return { message: "Ce champ doit être du texte" };
      }
      if (issue.expected === "number") {
        return { message: "Ce champ doit être un nombre" };
      }
      if (issue.expected === "boolean") {
        return { message: "Ce champ doit être vrai ou faux" };
      }
      return { message: "Type de donnée invalide" };
    
    case z.ZodIssueCode.too_small:
      if (issue.type === "string") {
        return { message: `Ce champ doit contenir au moins ${issue.minimum} caractères` };
      }
      if (issue.type === "number") {
        return { message: `Ce champ doit être supérieur ou égal à ${issue.minimum}` };
      }
      if (issue.type === "array") {
        return { message: `Ce champ doit contenir au moins ${issue.minimum} éléments` };
      }
      return { message: "Valeur trop petite" };
    
    case z.ZodIssueCode.too_big:
      if (issue.type === "string") {
        return { message: `Ce champ ne peut pas dépasser ${issue.maximum} caractères` };
      }
      if (issue.type === "number") {
        return { message: `Ce champ doit être inférieur ou égal à ${issue.maximum}` };
      }
      if (issue.type === "array") {
        return { message: `Ce champ ne peut pas contenir plus de ${issue.maximum} éléments` };
      }
      return { message: "Valeur trop grande" };
    
    case z.ZodIssueCode.invalid_string:
      if (issue.validation === "email") {
        return { message: "Format d'email invalide" };
      }
      if (issue.validation === "url") {
        return { message: "Format d'URL invalide" };
      }
      if (issue.validation === "regex") {
        return { message: "Format invalide" };
      }
      return { message: "Format de texte invalide" };
    
    case z.ZodIssueCode.invalid_date:
      return { message: "Date invalide" };
    
    default:
      return { message: ctx.defaultError };
  }
};

// Set the custom error map
z.setErrorMap(frenchErrorMap);

/**
 * Helper pour transformer les erreurs Zod en format utilisable
 */
export function formatZodErrors(error: z.ZodError): Record<string, string[]> {
  const errors: Record<string, string[]> = {};
  
  error.errors.forEach((err) => {
    const path = err.path.join(".");
    if (!errors[path]) {
      errors[path] = [];
    }
    errors[path].push(err.message);
  });
  
  return errors;
}

/**
 * Helper pour valider des données avec gestion d'erreurs
 */
export function validateData<T>(
  schema: z.ZodSchema<T>,
  data: unknown
): { success: true; data: T } | { success: false; errors: Record<string, string[]> } {
  try {
    const validatedData = schema.parse(data);
    return { success: true, data: validatedData };
  } catch (error) {
    if (error instanceof z.ZodError) {
      return { success: false, errors: formatZodErrors(error) };
    }
    return { 
      success: false, 
      errors: { _general: ["Erreur de validation inconnue"] } 
    };
  }
}
