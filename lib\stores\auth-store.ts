import { create } from "zustand";
import { devtools, persist } from "zustand/middleware";

export interface User {
  id: string;
  email: string;
  name: string;
  role: "admin" | "auditeur" | "manager" | "utilisateur" | "responsable_qualite" | "responsable_conformite";
  organizationId: string;
  avatar?: string;
  image?: string; // Compatibilité avec Better Auth
  isActive: boolean;
  lastLoginAt?: Date;
  createdAt: Date;
  updatedAt: Date;
}

interface AuthState {
  user: User | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  error: string | null;
  
  // Actions
  setUser: (user: User | null) => void;
  setLoading: (isLoading: boolean) => void;
  setError: (error: string | null) => void;
  login: (email: string, password: string) => Promise<void>;
  logout: () => void;
  clearError: () => void;
}

export const useAuthStore = create<AuthState>()(
  devtools(
    persist(
      (set, get) => ({
        user: null,
        isAuthenticated: false,
        isLoading: false,
        error: null,

        setUser: (user) =>
          set(
            { 
              user, 
              isAuthenticated: !!user,
              error: null 
            },
            false,
            "auth/setUser"
          ),

        setLoading: (isLoading) =>
          set({ isLoading }, false, "auth/setLoading"),

        setError: (error) =>
          set({ error }, false, "auth/setError"),

        login: async (email, password) => {
          try {
            set({ isLoading: true, error: null }, false, "auth/login/start");
            
            // TODO: Implémenter l'appel API
            const response = await fetch("/api/auth/login", {
              method: "POST",
              headers: { "Content-Type": "application/json" },
              body: JSON.stringify({ email, password }),
            });

            if (!response.ok) {
              throw new Error("Échec de la connexion");
            }

            const userData = await response.json();
            
            set(
              {
                user: userData,
                isAuthenticated: true,
                isLoading: false,
                error: null,
              },
              false,
              "auth/login/success"
            );
          } catch (error) {
            set(
              {
                error: error instanceof Error ? error.message : "Erreur de connexion",
                isLoading: false,
                isAuthenticated: false,
                user: null,
              },
              false,
              "auth/login/error"
            );
          }
        },

        logout: () => {
          set(
            {
              user: null,
              isAuthenticated: false,
              error: null,
            },
            false,
            "auth/logout"
          );
          
          // TODO: Appeler l'API de déconnexion
          fetch("/api/auth/logout", { method: "POST" });
        },

        clearError: () => set({ error: null }, false, "auth/clearError"),
      }),
      {
        name: "magneto-auth",
        partialize: (state) => ({
          user: state.user,
          isAuthenticated: state.isAuthenticated,
        }),
      }
    ),
    { name: "auth-store" }
  )
);
